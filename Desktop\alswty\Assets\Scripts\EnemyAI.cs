using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// ذكاء اصطناعي للأعداء والدفاعات الجوية
/// Enemy AI for air defenses and moving targets
/// </summary>
public class EnemyAI : MonoBehaviour
{
    [Header("إعدادات الذكاء الاصطناعي - AI Settings")]
    [SerializeField] private AIType aiType = AIType.AirDefense;
    [SerializeField] private float detectionRange = 300f;
    [SerializeField] private float attackRange = 200f;
    [SerializeField] private LayerMask targetLayers = -1;
    [SerializeField] private float reactionTime = 1f;
    
    [Header("إعدادات الحركة - Movement Settings")]
    [SerializeField] private float moveSpeed = 10f;
    [SerializeField] private float rotationSpeed = 90f;
    [SerializeField] private PatrolType patrolType = PatrolType.Waypoints;
    [SerializeField] private Transform[] waypoints;
    [SerializeField] private float waypointRadius = 2f;
    [SerializeField] private bool randomPatrol = false;
    
    [Header("إعدادات القتال - Combat Settings")]
    [SerializeField] private GameObject projectilePrefab;
    [SerializeField] private Transform firePoint;
    [SerializeField] private float fireRate = 2f;
    [SerializeField] private float projectileSpeed = 50f;
    [SerializeField] private int burstCount = 3;
    [SerializeField] private float burstDelay = 0.2f;
    
    [Header("إعدادات التفادي - Evasion Settings")]
    [SerializeField] private bool canEvade = true;
    [SerializeField] private float evasionRadius = 50f;
    [SerializeField] private float evasionSpeed = 15f;
    [SerializeField] private float evasionDuration = 3f;
    
    [Header("المؤثرات - Effects")]
    [SerializeField] private ParticleSystem muzzleFlash;
    [SerializeField] private AudioClip fireSound;
    [SerializeField] private AudioClip alertSound;
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private Light searchLight;
    
    // المتغيرات الداخلية - Internal Variables
    private Transform currentTarget;
    private AIState currentState = AIState.Patrol;
    private int currentWaypointIndex = 0;
    private float lastFireTime;
    private float lastDetectionTime;
    private bool isEvading = false;
    private Vector3 evasionDirection;
    private Rigidbody rb;
    private HealthSystem healthSystem;
    
    // قائمة الأهداف المكتشفة - Detected Targets List
    private List<Transform> detectedTargets = new List<Transform>();
    private List<Transform> incomingMissiles = new List<Transform>();
    
    // الأحداث - Events
    public System.Action<Transform> OnTargetDetected;
    public System.Action<Transform> OnTargetLost;
    public System.Action OnAlertStateEntered;
    public System.Action OnCombatStateEntered;
    
    public enum AIType
    {
        AirDefense,     // دفاع جوي ثابت
        MobileDefense,  // دفاع جوي متحرك
        Aircraft,       // طائرة
        GroundVehicle   // مركبة أرضية
    }
    
    public enum AIState
    {
        Patrol,         // دورية
        Alert,          // تأهب
        Combat,         // قتال
        Evading,        // تفادي
        Returning       // عودة
    }
    
    public enum PatrolType
    {
        Waypoints,      // نقاط محددة
        Random,         // عشوائي
        Circle,         // دائري
        BackAndForth    // ذهاب وإياب
    }
    
    void Start()
    {
        InitializeAI();
    }
    
    void Update()
    {
        UpdateAI();
        UpdateDetection();
        UpdateState();
    }
    
    /// <summary>
    /// تهيئة الذكاء الاصطناعي
    /// Initialize AI
    /// </summary>
    private void InitializeAI()
    {
        rb = GetComponent<Rigidbody>();
        healthSystem = GetComponent<HealthSystem>();
        
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        if (firePoint == null)
        {
            firePoint = transform;
        }
        
        // إعداد نقاط الدورية إذا لم تكن محددة
        if (waypoints == null || waypoints.Length == 0)
        {
            CreateDefaultWaypoints();
        }
        
        // ربط أحداث نظام الصحة
        if (healthSystem != null)
        {
            healthSystem.OnDamageTaken += OnDamageTaken;
            healthSystem.OnDeath += OnDeath;
        }
    }
    
    /// <summary>
    /// تحديث الذكاء الاصطناعي
    /// Update AI
    /// </summary>
    private void UpdateAI()
    {
        switch (currentState)
        {
            case AIState.Patrol:
                HandlePatrolState();
                break;
            case AIState.Alert:
                HandleAlertState();
                break;
            case AIState.Combat:
                HandleCombatState();
                break;
            case AIState.Evading:
                HandleEvadingState();
                break;
            case AIState.Returning:
                HandleReturningState();
                break;
        }
    }
    
    /// <summary>
    /// تحديث الكشف
    /// Update detection
    /// </summary>
    private void UpdateDetection()
    {
        DetectTargets();
        DetectIncomingMissiles();
        CleanupTargetLists();
    }
    
    /// <summary>
    /// كشف الأهداف
    /// Detect targets
    /// </summary>
    private void DetectTargets()
    {
        Collider[] potentialTargets = Physics.OverlapSphere(transform.position, detectionRange, targetLayers);
        
        foreach (Collider col in potentialTargets)
        {
            if (col.transform == transform) continue;
            
            // التحقق من خط البصر
            Vector3 directionToTarget = col.transform.position - transform.position;
            if (Physics.Raycast(transform.position, directionToTarget.normalized, 
                              directionToTarget.magnitude, ~targetLayers))
            {
                continue; // هناك عائق
            }
            
            // إضافة الهدف إذا لم يكن موجوداً
            if (!detectedTargets.Contains(col.transform))
            {
                detectedTargets.Add(col.transform);
                OnTargetDetected?.Invoke(col.transform);
                
                if (currentState == AIState.Patrol)
                {
                    ChangeState(AIState.Alert);
                }
            }
        }
    }
    
    /// <summary>
    /// كشف الصواريخ القادمة
    /// Detect incoming missiles
    /// </summary>
    private void DetectIncomingMissiles()
    {
        if (!canEvade) return;
        
        Collider[] missiles = Physics.OverlapSphere(transform.position, evasionRadius);
        
        foreach (Collider missile in missiles)
        {
            var missileController = missile.GetComponent<MissileController>();
            if (missileController != null && missileController.IsLaunched)
            {
                if (!incomingMissiles.Contains(missile.transform))
                {
                    incomingMissiles.Add(missile.transform);
                    
                    if (currentState != AIState.Evading)
                    {
                        StartEvasion();
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// تنظيف قوائم الأهداف
    /// Cleanup target lists
    /// </summary>
    private void CleanupTargetLists()
    {
        detectedTargets.RemoveAll(target => target == null || 
                                 Vector3.Distance(transform.position, target.position) > detectionRange * 1.5f);
        
        incomingMissiles.RemoveAll(missile => missile == null || 
                                  Vector3.Distance(transform.position, missile.position) > evasionRadius * 2f);
    }
    
    /// <summary>
    /// تحديث الحالة
    /// Update state
    /// </summary>
    private void UpdateState()
    {
        // التحقق من وجود أهداف
        if (detectedTargets.Count == 0 && currentState != AIState.Patrol && currentState != AIState.Returning)
        {
            ChangeState(AIState.Returning);
        }
        
        // اختيار أفضل هدف
        if (detectedTargets.Count > 0)
        {
            currentTarget = GetBestTarget();
            
            if (currentState == AIState.Alert)
            {
                float distanceToTarget = Vector3.Distance(transform.position, currentTarget.position);
                if (distanceToTarget <= attackRange)
                {
                    ChangeState(AIState.Combat);
                }
            }
        }
    }
    
    /// <summary>
    /// الحصول على أفضل هدف
    /// Get best target
    /// </summary>
    private Transform GetBestTarget()
    {
        if (detectedTargets.Count == 0) return null;
        
        Transform bestTarget = null;
        float bestScore = float.MinValue;
        
        foreach (Transform target in detectedTargets)
        {
            float distance = Vector3.Distance(transform.position, target.position);
            float score = 1000f / distance; // كلما قل المسافة، زاد النقاط
            
            // إعطاء أولوية للصواريخ
            if (target.GetComponent<MissileController>() != null)
            {
                score *= 2f;
            }
            
            if (score > bestScore)
            {
                bestScore = score;
                bestTarget = target;
            }
        }
        
        return bestTarget;
    }
    
    /// <summary>
    /// التعامل مع حالة الدورية
    /// Handle patrol state
    /// </summary>
    private void HandlePatrolState()
    {
        if (aiType == AIType.AirDefense) return; // الدفاع الجوي لا يتحرك
        
        switch (patrolType)
        {
            case PatrolType.Waypoints:
                PatrolWaypoints();
                break;
            case PatrolType.Random:
                PatrolRandom();
                break;
            case PatrolType.Circle:
                PatrolCircle();
                break;
            case PatrolType.BackAndForth:
                PatrolBackAndForth();
                break;
        }
    }
    
    /// <summary>
    /// دورية النقاط المحددة
    /// Patrol waypoints
    /// </summary>
    private void PatrolWaypoints()
    {
        if (waypoints == null || waypoints.Length == 0) return;
        
        Transform targetWaypoint = waypoints[currentWaypointIndex];
        MoveTowards(targetWaypoint.position);
        
        if (Vector3.Distance(transform.position, targetWaypoint.position) < waypointRadius)
        {
            if (randomPatrol)
            {
                currentWaypointIndex = Random.Range(0, waypoints.Length);
            }
            else
            {
                currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
            }
        }
    }
    
    /// <summary>
    /// دورية عشوائية
    /// Random patrol
    /// </summary>
    private void PatrolRandom()
    {
        // تنفيذ الدورية العشوائية
        if (Vector3.Distance(transform.position, waypoints[0].position) < waypointRadius)
        {
            Vector3 randomDirection = Random.insideUnitSphere * 50f;
            randomDirection.y = 0; // البقاء على نفس المستوى
            waypoints[0].position = transform.position + randomDirection;
        }
        
        MoveTowards(waypoints[0].position);
    }
    
    /// <summary>
    /// دورية دائرية
    /// Circle patrol
    /// </summary>
    private void PatrolCircle()
    {
        float angle = Time.time * moveSpeed * 0.1f;
        Vector3 center = waypoints[0].position;
        Vector3 targetPosition = center + new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle)) * 30f;
        
        MoveTowards(targetPosition);
    }
    
    /// <summary>
    /// دورية ذهاب وإياب
    /// Back and forth patrol
    /// </summary>
    private void PatrolBackAndForth()
    {
        if (waypoints.Length < 2) return;
        
        Transform targetWaypoint = waypoints[currentWaypointIndex];
        MoveTowards(targetWaypoint.position);
        
        if (Vector3.Distance(transform.position, targetWaypoint.position) < waypointRadius)
        {
            currentWaypointIndex = currentWaypointIndex == 0 ? 1 : 0;
        }
    }
    
    /// <summary>
    /// التعامل مع حالة التأهب
    /// Handle alert state
    /// </summary>
    private void HandleAlertState()
    {
        if (currentTarget != null)
        {
            // التوجه نحو الهدف
            LookAtTarget(currentTarget.position);
            
            // تشغيل الضوء الكاشف
            if (searchLight != null)
            {
                searchLight.enabled = true;
            }
        }
    }
    
    /// <summary>
    /// التعامل مع حالة القتال
    /// Handle combat state
    /// </summary>
    private void HandleCombatState()
    {
        if (currentTarget != null)
        {
            // التصويب والإطلاق
            LookAtTarget(currentTarget.position);
            
            if (Time.time - lastFireTime >= 1f / fireRate)
            {
                StartCoroutine(FireBurst());
                lastFireTime = Time.time;
            }
        }
    }
    
    /// <summary>
    /// التعامل مع حالة التفادي
    /// Handle evading state
    /// </summary>
    private void HandleEvadingState()
    {
        // الحركة في اتجاه التفادي
        MoveTowards(transform.position + evasionDirection);
    }
    
    /// <summary>
    /// التعامل مع حالة العودة
    /// Handle returning state
    /// </summary>
    private void HandleReturningState()
    {
        if (waypoints != null && waypoints.Length > 0)
        {
            MoveTowards(waypoints[0].position);
            
            if (Vector3.Distance(transform.position, waypoints[0].position) < waypointRadius)
            {
                ChangeState(AIState.Patrol);
            }
        }
    }
    
    /// <summary>
    /// الحركة نحو موقع
    /// Move towards position
    /// </summary>
    private void MoveTowards(Vector3 targetPosition)
    {
        Vector3 direction = (targetPosition - transform.position).normalized;
        
        if (rb != null)
        {
            rb.velocity = direction * moveSpeed;
        }
        else
        {
            transform.position += direction * moveSpeed * Time.deltaTime;
        }
        
        // التوجه نحو الاتجاه
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, 
                                                rotationSpeed * Time.deltaTime / 90f);
        }
    }
    
    /// <summary>
    /// النظر نحو الهدف
    /// Look at target
    /// </summary>
    private void LookAtTarget(Vector3 targetPosition)
    {
        Vector3 direction = (targetPosition - transform.position).normalized;
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, 
                                                rotationSpeed * Time.deltaTime / 90f);
        }
    }
    
    /// <summary>
    /// إطلاق رشقة
    /// Fire burst
    /// </summary>
    private IEnumerator FireBurst()
    {
        for (int i = 0; i < burstCount; i++)
        {
            FireProjectile();
            yield return new WaitForSeconds(burstDelay);
        }
    }
    
    /// <summary>
    /// إطلاق قذيفة
    /// Fire projectile
    /// </summary>
    private void FireProjectile()
    {
        if (projectilePrefab == null || currentTarget == null) return;
        
        // إنشاء القذيفة
        GameObject projectile = Instantiate(projectilePrefab, firePoint.position, firePoint.rotation);
        
        // إعطاء القذيفة سرعة
        Rigidbody projectileRb = projectile.GetComponent<Rigidbody>();
        if (projectileRb != null)
        {
            Vector3 direction = (currentTarget.position - firePoint.position).normalized;
            projectileRb.velocity = direction * projectileSpeed;
        }
        
        // تشغيل المؤثرات
        PlayFireEffects();
        
        // تدمير القذيفة بعد فترة
        Destroy(projectile, 10f);
    }
    
    /// <summary>
    /// تشغيل مؤثرات الإطلاق
    /// Play fire effects
    /// </summary>
    private void PlayFireEffects()
    {
        if (muzzleFlash != null)
        {
            muzzleFlash.Play();
        }
        
        if (fireSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(fireSound);
        }
    }
    
    /// <summary>
    /// بدء التفادي
    /// Start evasion
    /// </summary>
    private void StartEvasion()
    {
        ChangeState(AIState.Evading);
        
        // حساب اتجاه التفادي
        Vector3 averageMissileDirection = Vector3.zero;
        foreach (Transform missile in incomingMissiles)
        {
            averageMissileDirection += (missile.position - transform.position).normalized;
        }
        
        if (incomingMissiles.Count > 0)
        {
            averageMissileDirection /= incomingMissiles.Count;
            evasionDirection = Vector3.Cross(averageMissileDirection, Vector3.up).normalized;
            
            // إضافة عشوائية للتفادي
            evasionDirection += Random.insideUnitSphere * 0.3f;
            evasionDirection.y = 0;
            evasionDirection.Normalize();
        }
        
        StartCoroutine(EvasionCoroutine());
    }
    
    /// <summary>
    /// عملية التفادي
    /// Evasion coroutine
    /// </summary>
    private IEnumerator EvasionCoroutine()
    {
        isEvading = true;
        float originalSpeed = moveSpeed;
        moveSpeed = evasionSpeed;
        
        yield return new WaitForSeconds(evasionDuration);
        
        moveSpeed = originalSpeed;
        isEvading = false;
        
        if (detectedTargets.Count > 0)
        {
            ChangeState(AIState.Combat);
        }
        else
        {
            ChangeState(AIState.Returning);
        }
    }
    
    /// <summary>
    /// تغيير الحالة
    /// Change state
    /// </summary>
    private void ChangeState(AIState newState)
    {
        if (currentState == newState) return;
        
        currentState = newState;
        
        switch (newState)
        {
            case AIState.Alert:
                OnAlertStateEntered?.Invoke();
                if (alertSound != null && audioSource != null)
                {
                    audioSource.PlayOneShot(alertSound);
                }
                break;
            case AIState.Combat:
                OnCombatStateEntered?.Invoke();
                break;
        }
    }
    
    /// <summary>
    /// إنشاء نقاط دورية افتراضية
    /// Create default waypoints
    /// </summary>
    private void CreateDefaultWaypoints()
    {
        GameObject waypointParent = new GameObject("Waypoints_" + gameObject.name);
        waypoints = new Transform[4];
        
        for (int i = 0; i < 4; i++)
        {
            GameObject waypoint = new GameObject("Waypoint_" + i);
            waypoint.transform.parent = waypointParent.transform;
            
            float angle = i * 90f * Mathf.Deg2Rad;
            Vector3 position = transform.position + new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle)) * 50f;
            waypoint.transform.position = position;
            
            waypoints[i] = waypoint.transform;
        }
    }
    
    /// <summary>
    /// عند تلقي ضرر
    /// When taking damage
    /// </summary>
    private void OnDamageTaken(float damage)
    {
        if (currentState == AIState.Patrol)
        {
            ChangeState(AIState.Alert);
        }
    }
    
    /// <summary>
    /// عند الموت
    /// When dying
    /// </summary>
    private void OnDeath()
    {
        // إيقاف جميع العمليات
        StopAllCoroutines();
        
        // تعطيل الذكاء الاصطناعي
        enabled = false;
    }
    
    /// <summary>
    /// رسم المساعدات البصرية
    /// Draw visual aids
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        // رسم نطاق الكشف
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // رسم نطاق الهجوم
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // رسم نطاق التفادي
        if (canEvade)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(transform.position, evasionRadius);
        }
        
        // رسم نقاط الدورية
        if (waypoints != null)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < waypoints.Length; i++)
            {
                if (waypoints[i] != null)
                {
                    Gizmos.DrawWireSphere(waypoints[i].position, waypointRadius);
                    
                    if (i < waypoints.Length - 1 && waypoints[i + 1] != null)
                    {
                        Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
                    }
                }
            }
        }
        
        // رسم خط إلى الهدف الحالي
        if (currentTarget != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawLine(transform.position, currentTarget.position);
        }
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public AIState CurrentState => currentState;
    public Transform CurrentTarget => currentTarget;
    public List<Transform> DetectedTargets => new List<Transform>(detectedTargets);
    public bool IsEvading => isEvading;
}
