using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// مدير اللعبة الرئيسي - الوعد الصادق 3
/// Main Game Manager - Al-Wa'ad Al-Sadiq 3
/// </summary>
public class GameManager : MonoBeh<PERSON>our
{
    [Header("إعدادات اللعبة - Game Settings")]
    [SerializeField] private GameMode currentGameMode = GameMode.Campaign;
    [SerializeField] private DifficultyLevel difficulty = DifficultyLevel.Normal;
    [SerializeField] private int currentLevel = 1;
    [SerializeField] private int maxLevels = 10;
    
    [Header("إعدادات النقاط - Scoring Settings")]
    [SerializeField] private int playerScore = 0;
    [SerializeField] private int targetScore = 1000;
    [SerializeField] private int missileHitPoints = 100;
    [SerializeField] private int targetDestroyedPoints = 500;
    [SerializeField] private int accuracyBonusPoints = 50;
    
    [Header("إعدادات الوقت - Time Settings")]
    [SerializeField] private float levelTimeLimit = 300f; // 5 دقائق
    [SerializeField] private float currentTime;
    [SerializeField] private bool useTimeLimit = true;
    
    [Header("إعدادات الأهداف - Objectives Settings")]
    [SerializeField] private int totalTargets = 10;
    [SerializeField] private int targetsDestroyed = 0;
    [SerializeField] private int missilesFired = 0;
    [SerializeField] private int missilesHit = 0;
    
    [Header("مراجع المكونات - Component References")]
    [SerializeField] private LaunchPlatform launchPlatform;
    [SerializeField] private UIManager uiManager;
    [SerializeField] private AudioManager audioManager;
    [SerializeField] private CameraController cameraController;
    
    [Header("إعدادات الصوت - Audio Settings")]
    [SerializeField] private AudioClip backgroundMusic;
    [SerializeField] private AudioClip victorySound;
    [SerializeField] private AudioClip defeatSound;
    [SerializeField] private AudioClip levelCompleteSound;
    
    // المتغيرات الداخلية - Internal Variables
    private GameState currentGameState = GameState.Playing;
    private bool isPaused = false;
    private float gameStartTime;
    private List<GameObject> activeTargets = new List<GameObject>();
    private List<EnemyAI> activeEnemies = new List<EnemyAI>();
    
    // الإحصائيات - Statistics
    private GameStatistics gameStats = new GameStatistics();
    
    // المثيل الوحيد - Singleton Instance
    public static GameManager Instance { get; private set; }
    
    // الأحداث - Events
    public System.Action<GameState> OnGameStateChanged;
    public System.Action<int> OnScoreChanged;
    public System.Action<int, int> OnTargetsChanged; // destroyed, total
    public System.Action<float> OnTimeChanged;
    public System.Action<int> OnLevelChanged;
    public System.Action<GameStatistics> OnGameCompleted;
    
    public enum GameMode
    {
        Campaign,       // حملة
        Survival,       // البقاء
        TimeAttack,     // هجوم الوقت
        Training        // التدريب
    }
    
    public enum DifficultyLevel
    {
        Easy,           // سهل
        Normal,         // عادي
        Hard,           // صعب
        Expert          // خبير
    }
    
    public enum GameState
    {
        MainMenu,       // القائمة الرئيسية
        Playing,        // اللعب
        Paused,         // متوقف
        Victory,        // النصر
        Defeat,         // الهزيمة
        Loading         // التحميل
    }
    
    [System.Serializable]
    public class GameStatistics
    {
        public float totalPlayTime;
        public int totalMissilesFired;
        public int totalTargetsDestroyed;
        public int totalScore;
        public float accuracy;
        public int levelsCompleted;
        public DifficultyLevel difficulty;
        public GameMode gameMode;
    }
    
    void Awake()
    {
        // تطبيق نمط Singleton
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }
        
        InitializeGame();
    }
    
    void Start()
    {
        StartGame();
    }
    
    void Update()
    {
        if (currentGameState == GameState.Playing && !isPaused)
        {
            UpdateGameTime();
            UpdateGameLogic();
            HandleInput();
        }
    }
    
    /// <summary>
    /// تهيئة اللعبة
    /// Initialize game
    /// </summary>
    private void InitializeGame()
    {
        // العثور على المكونات المطلوبة
        if (launchPlatform == null)
            launchPlatform = FindObjectOfType<LaunchPlatform>();
        
        if (uiManager == null)
            uiManager = FindObjectOfType<UIManager>();
        
        if (audioManager == null)
            audioManager = FindObjectOfType<AudioManager>();
        
        if (cameraController == null)
            cameraController = FindObjectOfType<CameraController>();
        
        // ربط الأحداث
        BindEvents();
        
        // تطبيق إعدادات الصعوبة
        ApplyDifficultySettings();
    }
    
    /// <summary>
    /// بدء اللعبة
    /// Start game
    /// </summary>
    public void StartGame()
    {
        gameStartTime = Time.time;
        currentTime = levelTimeLimit;
        
        ChangeGameState(GameState.Playing);
        
        // تشغيل الموسيقى الخلفية
        if (backgroundMusic != null && audioManager != null)
        {
            audioManager.PlayBackgroundMusic(backgroundMusic);
        }
        
        // تحديث واجهة المستخدم
        UpdateUI();
        
        // العثور على جميع الأهداف في المستوى
        FindAllTargets();
    }
    
    /// <summary>
    /// إيقاف/استئناف اللعبة
    /// Pause/Resume game
    /// </summary>
    public void TogglePause()
    {
        isPaused = !isPaused;
        Time.timeScale = isPaused ? 0f : 1f;
        
        if (isPaused)
        {
            ChangeGameState(GameState.Paused);
        }
        else
        {
            ChangeGameState(GameState.Playing);
        }
    }
    
    /// <summary>
    /// تحديث وقت اللعبة
    /// Update game time
    /// </summary>
    private void UpdateGameTime()
    {
        if (useTimeLimit)
        {
            currentTime -= Time.deltaTime;
            OnTimeChanged?.Invoke(currentTime);
            
            if (currentTime <= 0)
            {
                GameOver(false); // انتهى الوقت
            }
        }
    }
    
    /// <summary>
    /// تحديث منطق اللعبة
    /// Update game logic
    /// </summary>
    private void UpdateGameLogic()
    {
        // تحديث الإحصائيات
        gameStats.totalPlayTime = Time.time - gameStartTime;
        gameStats.accuracy = missilesFired > 0 ? (float)missilesHit / missilesFired * 100f : 0f;
        
        // التحقق من شروط النصر
        CheckVictoryConditions();
        
        // التحقق من شروط الهزيمة
        CheckDefeatConditions();
    }
    
    /// <summary>
    /// التحكم في الإدخال
    /// Handle input
    /// </summary>
    private void HandleInput()
    {
        // إيقاف/استئناف اللعبة
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            TogglePause();
        }
        
        // إعادة تشغيل المستوى
        if (Input.GetKeyDown(KeyCode.R) && Input.GetKey(KeyCode.LeftControl))
        {
            RestartLevel();
        }
        
        // الانتقال للمستوى التالي (للاختبار)
        if (Input.GetKeyDown(KeyCode.N) && Input.GetKey(KeyCode.LeftControl))
        {
            NextLevel();
        }
    }
    
    /// <summary>
    /// ربط الأحداث
    /// Bind events
    /// </summary>
    private void BindEvents()
    {
        if (launchPlatform != null)
        {
            launchPlatform.OnMissileCountChanged += OnMissileCountChanged;
        }
        
        // البحث عن جميع الأعداء وربط أحداثهم
        EnemyAI[] enemies = FindObjectsOfType<EnemyAI>();
        foreach (EnemyAI enemy in enemies)
        {
            activeEnemies.Add(enemy);
            
            var healthSystem = enemy.GetComponent<HealthSystem>();
            if (healthSystem != null)
            {
                healthSystem.OnDeath += () => OnTargetDestroyed(enemy.gameObject);
            }
        }
    }
    
    /// <summary>
    /// تطبيق إعدادات الصعوبة
    /// Apply difficulty settings
    /// </summary>
    private void ApplyDifficultySettings()
    {
        switch (difficulty)
        {
            case DifficultyLevel.Easy:
                levelTimeLimit *= 1.5f;
                missileHitPoints = (int)(missileHitPoints * 1.2f);
                break;
            case DifficultyLevel.Normal:
                // الإعدادات الافتراضية
                break;
            case DifficultyLevel.Hard:
                levelTimeLimit *= 0.8f;
                missileHitPoints = (int)(missileHitPoints * 0.8f);
                break;
            case DifficultyLevel.Expert:
                levelTimeLimit *= 0.6f;
                missileHitPoints = (int)(missileHitPoints * 0.6f);
                break;
        }
    }
    
    /// <summary>
    /// العثور على جميع الأهداف
    /// Find all targets
    /// </summary>
    private void FindAllTargets()
    {
        activeTargets.Clear();
        
        // البحث عن الأهداف بالعلامات
        GameObject[] targets = GameObject.FindGameObjectsWithTag("Enemy");
        activeTargets.AddRange(targets);
        
        totalTargets = activeTargets.Count;
        OnTargetsChanged?.Invoke(targetsDestroyed, totalTargets);
    }
    
    /// <summary>
    /// التحقق من شروط النصر
    /// Check victory conditions
    /// </summary>
    private void CheckVictoryConditions()
    {
        bool victory = false;
        
        switch (currentGameMode)
        {
            case GameMode.Campaign:
                victory = targetsDestroyed >= totalTargets;
                break;
            case GameMode.Survival:
                victory = playerScore >= targetScore;
                break;
            case GameMode.TimeAttack:
                victory = targetsDestroyed >= totalTargets && currentTime > 0;
                break;
            case GameMode.Training:
                victory = targetsDestroyed >= totalTargets;
                break;
        }
        
        if (victory)
        {
            GameOver(true);
        }
    }
    
    /// <summary>
    /// التحقق من شروط الهزيمة
    /// Check defeat conditions
    /// </summary>
    private void CheckDefeatConditions()
    {
        bool defeat = false;
        
        switch (currentGameMode)
        {
            case GameMode.Campaign:
                defeat = useTimeLimit && currentTime <= 0 && targetsDestroyed < totalTargets;
                break;
            case GameMode.Survival:
                // في وضع البقاء، لا توجد هزيمة تقليدية
                break;
            case GameMode.TimeAttack:
                defeat = currentTime <= 0 && targetsDestroyed < totalTargets;
                break;
        }
        
        if (defeat)
        {
            GameOver(false);
        }
    }
    
    /// <summary>
    /// انتهاء اللعبة
    /// Game over
    /// </summary>
    public void GameOver(bool victory)
    {
        if (victory)
        {
            ChangeGameState(GameState.Victory);
            
            if (victorySound != null && audioManager != null)
            {
                audioManager.PlaySoundEffect(victorySound);
            }
            
            // إضافة مكافآت النصر
            AddVictoryBonus();
            
            // حفظ التقدم
            SaveProgress();
        }
        else
        {
            ChangeGameState(GameState.Defeat);
            
            if (defeatSound != null && audioManager != null)
            {
                audioManager.PlaySoundEffect(defeatSound);
            }
        }
        
        // تحديث الإحصائيات النهائية
        UpdateFinalStatistics();
        
        // إشعار واجهة المستخدم
        OnGameCompleted?.Invoke(gameStats);
    }
    
    /// <summary>
    /// إضافة مكافآت النصر
    /// Add victory bonus
    /// </summary>
    private void AddVictoryBonus()
    {
        // مكافأة الوقت المتبقي
        if (useTimeLimit && currentTime > 0)
        {
            int timeBonus = Mathf.RoundToInt(currentTime * 10);
            AddScore(timeBonus);
        }
        
        // مكافأة الدقة
        if (gameStats.accuracy >= 80f)
        {
            AddScore(accuracyBonusPoints * 2);
        }
        else if (gameStats.accuracy >= 60f)
        {
            AddScore(accuracyBonusPoints);
        }
        
        // مكافأة الصعوبة
        int difficultyMultiplier = (int)difficulty + 1;
        AddScore(playerScore * difficultyMultiplier / 10);
    }
    
    /// <summary>
    /// تحديث الإحصائيات النهائية
    /// Update final statistics
    /// </summary>
    private void UpdateFinalStatistics()
    {
        gameStats.totalScore = playerScore;
        gameStats.totalMissilesFired = missilesFired;
        gameStats.totalTargetsDestroyed = targetsDestroyed;
        gameStats.difficulty = difficulty;
        gameStats.gameMode = currentGameMode;
        
        if (currentGameState == GameState.Victory)
        {
            gameStats.levelsCompleted = currentLevel;
        }
    }
    
    /// <summary>
    /// إضافة نقاط
    /// Add score
    /// </summary>
    public void AddScore(int points)
    {
        playerScore += points;
        OnScoreChanged?.Invoke(playerScore);
    }
    
    /// <summary>
    /// عند تدمير هدف
    /// When target is destroyed
    /// </summary>
    public void OnTargetDestroyed(GameObject target)
    {
        if (activeTargets.Contains(target))
        {
            activeTargets.Remove(target);
            targetsDestroyed++;
            
            AddScore(targetDestroyedPoints);
            OnTargetsChanged?.Invoke(targetsDestroyed, totalTargets);
        }
    }
    
    /// <summary>
    /// عند إطلاق صاروخ
    /// When missile is fired
    /// </summary>
    public void OnMissileFired()
    {
        missilesFired++;
    }
    
    /// <summary>
    /// عند إصابة هدف
    /// When target is hit
    /// </summary>
    public void OnTargetHit()
    {
        missilesHit++;
        AddScore(missileHitPoints);
    }
    
    /// <summary>
    /// عند تغيير عدد الصواريخ
    /// When missile count changes
    /// </summary>
    private void OnMissileCountChanged(int newCount)
    {
        // يمكن إضافة منطق إضافي هنا
    }
    
    /// <summary>
    /// الانتقال للمستوى التالي
    /// Next level
    /// </summary>
    public void NextLevel()
    {
        if (currentLevel < maxLevels)
        {
            currentLevel++;
            OnLevelChanged?.Invoke(currentLevel);
            
            // تحميل المستوى التالي
            LoadLevel(currentLevel);
        }
        else
        {
            // انتهت جميع المستويات
            CompleteGame();
        }
    }
    
    /// <summary>
    /// إعادة تشغيل المستوى
    /// Restart level
    /// </summary>
    public void RestartLevel()
    {
        LoadLevel(currentLevel);
    }
    
    /// <summary>
    /// تحميل مستوى
    /// Load level
    /// </summary>
    public void LoadLevel(int levelNumber)
    {
        ChangeGameState(GameState.Loading);
        
        // إعادة تعيين المتغيرات
        ResetLevelVariables();
        
        // تحميل المشهد
        string sceneName = "Level_" + levelNumber.ToString("00");
        SceneManager.LoadScene(sceneName);
    }
    
    /// <summary>
    /// إعادة تعيين متغيرات المستوى
    /// Reset level variables
    /// </summary>
    private void ResetLevelVariables()
    {
        targetsDestroyed = 0;
        missilesFired = 0;
        missilesHit = 0;
        currentTime = levelTimeLimit;
        activeTargets.Clear();
        activeEnemies.Clear();
    }
    
    /// <summary>
    /// إكمال اللعبة
    /// Complete game
    /// </summary>
    private void CompleteGame()
    {
        // تشغيل صوت إكمال اللعبة
        if (levelCompleteSound != null && audioManager != null)
        {
            audioManager.PlaySoundEffect(levelCompleteSound);
        }
        
        // حفظ الإحصائيات النهائية
        SaveFinalStatistics();
        
        // العودة للقائمة الرئيسية
        SceneManager.LoadScene("MainMenu");
    }
    
    /// <summary>
    /// تغيير حالة اللعبة
    /// Change game state
    /// </summary>
    private void ChangeGameState(GameState newState)
    {
        currentGameState = newState;
        OnGameStateChanged?.Invoke(newState);
    }
    
    /// <summary>
    /// تحديث واجهة المستخدم
    /// Update UI
    /// </summary>
    private void UpdateUI()
    {
        OnScoreChanged?.Invoke(playerScore);
        OnTargetsChanged?.Invoke(targetsDestroyed, totalTargets);
        OnTimeChanged?.Invoke(currentTime);
        OnLevelChanged?.Invoke(currentLevel);
    }
    
    /// <summary>
    /// حفظ التقدم
    /// Save progress
    /// </summary>
    private void SaveProgress()
    {
        PlayerPrefs.SetInt("CurrentLevel", currentLevel);
        PlayerPrefs.SetInt("HighScore", Mathf.Max(playerScore, PlayerPrefs.GetInt("HighScore", 0)));
        PlayerPrefs.SetFloat("BestAccuracy", Mathf.Max(gameStats.accuracy, PlayerPrefs.GetFloat("BestAccuracy", 0f)));
        PlayerPrefs.Save();
    }
    
    /// <summary>
    /// حفظ الإحصائيات النهائية
    /// Save final statistics
    /// </summary>
    private void SaveFinalStatistics()
    {
        // يمكن حفظ الإحصائيات في ملف JSON أو قاعدة بيانات
        string statsJson = JsonUtility.ToJson(gameStats);
        PlayerPrefs.SetString("GameStatistics", statsJson);
        PlayerPrefs.Save();
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public GameState CurrentGameState => currentGameState;
    public GameMode CurrentGameMode => currentGameMode;
    public DifficultyLevel CurrentDifficulty => difficulty;
    public int CurrentLevel => currentLevel;
    public int PlayerScore => playerScore;
    public float CurrentTime => currentTime;
    public int TargetsDestroyed => targetsDestroyed;
    public int TotalTargets => totalTargets;
    public float Accuracy => gameStats.accuracy;
    public bool IsPaused => isPaused;
    public GameStatistics Stats => gameStats;
}
