using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// مدير الأصوات الواقعية
/// Real Audio Manager for Realistic Sounds
/// </summary>
public class RealAudioManager : MonoBehaviour
{
    [Header("إعدادات الأصوات الواقعية - Real Audio Settings")]
    [SerializeField] private AudioSource musicSource;
    [SerializeField] private AudioSource sfxSource;
    [SerializeField] private AudioSource voiceSource;
    [SerializeField] private AudioSource ambientSource;
    [SerializeField] private float masterVolume = 1f;
    [SerializeField] private float musicVolume = 0.7f;
    [SerializeField] private float sfxVolume = 0.9f;
    [SerializeField] private float voiceVolume = 1f;
    [SerializeField] private float ambientVolume = 0.5f;
    [SerializeField] private float aircraftVolume = 0.2f; // مستوى منخفض للطائرات
    
    [Header("أصوات إطلاق الصواريخ - Missile Launch Sounds")]
    [SerializeField] private AudioClip[] missileLaunchSounds;
    [SerializeField] private AudioClip[] missileFlightSounds;
    [SerializeField] private AudioClip[] hypersonicBoomSounds;
    
    [Header("أصوات الانفجارات - Explosion Sounds")]
    [SerializeField] private AudioClip[] explosionLargeSounds;
    [SerializeField] private AudioClip[] explosionMediumSounds;
    [SerializeField] private AudioClip[] explosionSmallSounds;
    [SerializeField] private AudioClip[] impactHitSounds;
    [SerializeField] private AudioClip[] debrisFallSounds;
    
    [Header("أصوات الطائرات - Aircraft Sounds")]
    [SerializeField] private AudioClip[] fighterJetSounds;
    [SerializeField] private AudioClip[] helicopterSounds;
    [SerializeField] private AudioClip[] aircraftEngineSounds;
    [SerializeField] private AudioClip[] aircraftFlyBySounds;
    
    [Header("الموسيقى والخلفية - Music & Background")]
    [SerializeField] private AudioClip[] backgroundMusic;
    [SerializeField] private AudioClip[] combatMusic;
    [SerializeField] private AudioClip[] victoryMusic;
    [SerializeField] private AudioClip[] ambientWindSounds;
    [SerializeField] private AudioClip[] radioChatterSounds;
    
    [Header("أصوات واجهة المستخدم - UI Sounds")]
    [SerializeField] private AudioClip[] buttonClickSounds;
    [SerializeField] private AudioClip[] notificationSounds;
    [SerializeField] private AudioClip[] warningAlarmSounds;
    [SerializeField] private AudioClip[] reloadSounds;
    [SerializeField] private AudioClip[] targetLockSounds;
    
    [Header("الأصوات العربية - Arabic Voice")]
    [SerializeField] private AudioClip[] arabicLaunchSounds;
    [SerializeField] private AudioClip[] arabicTargetAcquiredSounds;
    [SerializeField] private AudioClip[] arabicHitConfirmedSounds;
    [SerializeField] private AudioClip[] arabicMissionCompleteSounds;
    [SerializeField] private AudioClip[] arabicReloadSounds;
    
    [Header("إعدادات التأثيرات الصوتية - Audio Effects Settings")]
    [SerializeField] private bool use3DAudio = true;
    [SerializeField] private bool useReverb = true;
    [SerializeField] private bool useDopplerEffect = true;
    [SerializeField] private float maxDistance = 500f;
    [SerializeField] private AnimationCurve distanceAttenuation;
    
    // المتغيرات الداخلية - Internal Variables
    private Dictionary<string, AudioClip[]> soundLibrary = new Dictionary<string, AudioClip[]>();
    private List<AudioSource> activeAudioSources = new List<AudioSource>();
    private Queue<AudioSource> audioSourcePool = new Queue<AudioSource>();
    private Coroutine currentMusicCoroutine;
    private bool isInitialized = false;
    
    // المثيل الوحيد - Singleton Instance
    public static RealAudioManager Instance { get; private set; }
    
    // الأحداث - Events
    public System.Action<string, AudioClip> OnSoundPlayed;
    public System.Action<string, AudioClip> OnMusicChanged;
    public System.Action OnAudioSystemInitialized;
    
    void Awake()
    {
        // تطبيق نمط Singleton
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeRealAudioSystem();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        StartCoroutine(LoadAllAudioClips());
    }
    
    /// <summary>
    /// تهيئة نظام الأصوات الواقعية
    /// Initialize real audio system
    /// </summary>
    private void InitializeRealAudioSystem()
    {
        // إنشاء مصادر الصوت إذا لم تكن موجودة
        CreateAudioSources();
        
        // إنشاء مجموعة مصادر الصوت
        CreateAudioSourcePool();
        
        // إعداد منحنى التخفيف الافتراضي
        if (distanceAttenuation == null)
        {
            distanceAttenuation = AnimationCurve.EaseInOut(0f, 1f, 1f, 0f);
        }
        
        Debug.Log("🔊 تم تهيئة نظام الأصوات الواقعية");
    }
    
    /// <summary>
    /// إنشاء مصادر الصوت
    /// Create audio sources
    /// </summary>
    private void CreateAudioSources()
    {
        if (musicSource == null)
        {
            GameObject musicObj = new GameObject("RealMusicSource");
            musicObj.transform.SetParent(transform);
            musicSource = musicObj.AddComponent<AudioSource>();
            musicSource.loop = true;
            musicSource.playOnAwake = false;
            musicSource.priority = 64;
        }
        
        if (sfxSource == null)
        {
            GameObject sfxObj = new GameObject("RealSFXSource");
            sfxObj.transform.SetParent(transform);
            sfxSource = sfxObj.AddComponent<AudioSource>();
            sfxSource.playOnAwake = false;
            sfxSource.priority = 128;
        }
        
        if (voiceSource == null)
        {
            GameObject voiceObj = new GameObject("RealVoiceSource");
            voiceObj.transform.SetParent(transform);
            voiceSource = voiceObj.AddComponent<AudioSource>();
            voiceSource.playOnAwake = false;
            voiceSource.priority = 0; // أعلى أولوية
        }
        
        if (ambientSource == null)
        {
            GameObject ambientObj = new GameObject("RealAmbientSource");
            ambientObj.transform.SetParent(transform);
            ambientSource = ambientObj.AddComponent<AudioSource>();
            ambientSource.loop = true;
            ambientSource.playOnAwake = false;
            ambientSource.priority = 200;
        }
    }
    
    /// <summary>
    /// إنشاء مجموعة مصادر الصوت
    /// Create audio source pool
    /// </summary>
    private void CreateAudioSourcePool()
    {
        for (int i = 0; i < 30; i++)
        {
            GameObject sourceObj = new GameObject($"RealPooledAudioSource_{i}");
            sourceObj.transform.SetParent(transform);
            AudioSource source = sourceObj.AddComponent<AudioSource>();
            source.playOnAwake = false;
            source.spatialBlend = use3DAudio ? 1f : 0f;
            source.rolloffMode = AudioRolloffMode.Custom;
            source.maxDistance = maxDistance;
            
            if (distanceAttenuation != null)
            {
                source.SetCustomCurve(AudioSourceCurveType.CustomRolloff, distanceAttenuation);
            }
            
            audioSourcePool.Enqueue(source);
        }
    }
    
    /// <summary>
    /// تحميل جميع الملفات الصوتية
    /// Load all audio clips
    /// </summary>
    private IEnumerator LoadAllAudioClips()
    {
        yield return new WaitForSeconds(0.1f);
        
        // تحميل أصوات الصواريخ
        soundLibrary["missile_launch"] = missileLaunchSounds;
        soundLibrary["missile_flight"] = missileFlightSounds;
        soundLibrary["hypersonic_boom"] = hypersonicBoomSounds;
        
        // تحميل أصوات الانفجارات
        soundLibrary["explosion_large"] = explosionLargeSounds;
        soundLibrary["explosion_medium"] = explosionMediumSounds;
        soundLibrary["explosion_small"] = explosionSmallSounds;
        soundLibrary["impact_hit"] = impactHitSounds;
        soundLibrary["debris_fall"] = debrisFallSounds;
        
        // تحميل أصوات الطائرات
        soundLibrary["fighter_jet"] = fighterJetSounds;
        soundLibrary["helicopter"] = helicopterSounds;
        soundLibrary["aircraft_engine"] = aircraftEngineSounds;
        soundLibrary["aircraft_flyby"] = aircraftFlyBySounds;
        
        // تحميل الموسيقى والخلفية
        soundLibrary["background_music"] = backgroundMusic;
        soundLibrary["combat_music"] = combatMusic;
        soundLibrary["victory_music"] = victoryMusic;
        soundLibrary["ambient_wind"] = ambientWindSounds;
        soundLibrary["radio_chatter"] = radioChatterSounds;
        
        // تحميل أصوات واجهة المستخدم
        soundLibrary["button_click"] = buttonClickSounds;
        soundLibrary["notification"] = notificationSounds;
        soundLibrary["warning_alarm"] = warningAlarmSounds;
        soundLibrary["reload"] = reloadSounds;
        soundLibrary["target_lock"] = targetLockSounds;
        
        // تحميل الأصوات العربية
        soundLibrary["arabic_launch"] = arabicLaunchSounds;
        soundLibrary["arabic_target_acquired"] = arabicTargetAcquiredSounds;
        soundLibrary["arabic_hit_confirmed"] = arabicHitConfirmedSounds;
        soundLibrary["arabic_mission_complete"] = arabicMissionCompleteSounds;
        soundLibrary["arabic_reload"] = arabicReloadSounds;
        
        isInitialized = true;
        OnAudioSystemInitialized?.Invoke();
        
        Debug.Log($"📚 تم تحميل {soundLibrary.Count} مجموعة صوتية واقعية");
        
        // تشغيل الموسيقى الخلفية
        PlayBackgroundMusic();
    }
    
    /// <summary>
    /// تشغيل صوت إطلاق الصاروخ الواقعي
    /// Play realistic missile launch sound
    /// </summary>
    public void PlayMissileLaunch(Vector3 position, float intensity = 1f)
    {
        AudioClip clip = GetRandomClip("missile_launch");
        if (clip != null)
        {
            PlaySound3D(clip, position, intensity);
            OnSoundPlayed?.Invoke("missile_launch", clip);
            
            // تشغيل صوت عربي متأخر
            StartCoroutine(PlayDelayedArabicSound("arabic_launch", 0.5f));
            
            Debug.Log("🚀 تشغيل صوت إطلاق صاروخ D610S واقعي");
        }
    }
    
    /// <summary>
    /// تشغيل صوت انفجار واقعي
    /// Play realistic explosion sound
    /// </summary>
    public void PlayExplosion(Vector3 position, ExplosionType type = ExplosionType.Large, float intensity = 1f)
    {
        string soundKey = "";
        switch (type)
        {
            case ExplosionType.Large:
                soundKey = "explosion_large";
                break;
            case ExplosionType.Medium:
                soundKey = "explosion_medium";
                break;
            case ExplosionType.Small:
                soundKey = "explosion_small";
                break;
        }
        
        AudioClip clip = GetRandomClip(soundKey);
        if (clip != null)
        {
            PlaySound3D(clip, position, intensity);
            OnSoundPlayed?.Invoke(soundKey, clip);
            
            // تشغيل صوت حطام متأخر
            if (type == ExplosionType.Large)
            {
                StartCoroutine(PlayDelayedDebrisSound(position, 0.8f));
            }
            
            Debug.Log($"💥 تشغيل صوت انفجار {type} واقعي");
        }
    }
    
    /// <summary>
    /// تشغيل صوت طائرة واقعي خفيف
    /// Play realistic aircraft sound (quiet)
    /// </summary>
    public void PlayAircraftSound(Vector3 position, AircraftType type, float intensity = 1f)
    {
        string soundKey = "";
        switch (type)
        {
            case AircraftType.Fighter:
                soundKey = "fighter_jet";
                break;
            case AircraftType.Helicopter:
                soundKey = "helicopter";
                break;
            case AircraftType.Generic:
                soundKey = "aircraft_engine";
                break;
        }

        AudioClip clip = GetRandomClip(soundKey);
        if (clip != null)
        {
            AudioSource source = GetPooledAudioSource();
            if (source != null)
            {
                source.transform.position = position;
                source.clip = clip;
                // استخدام مستوى الصوت المخصص للطائرات
                source.volume = masterVolume * aircraftVolume * intensity;
                source.loop = true;
                source.Play();

                activeAudioSources.Add(source);
                OnSoundPlayed?.Invoke(soundKey, clip);

                Debug.Log($"✈️ تشغيل صوت طائرة {type} خفيف");
            }
        }
    }
    
    /// <summary>
    /// تشغيل صوت انفجار صوتي فرط صوتي
    /// Play hypersonic boom sound
    /// </summary>
    public void PlayHypersonicBoom(Vector3 position, float intensity = 1f)
    {
        AudioClip clip = GetRandomClip("hypersonic_boom");
        if (clip != null)
        {
            PlaySound3D(clip, position, intensity * 1.2f);
            OnSoundPlayed?.Invoke("hypersonic_boom", clip);
            
            Debug.log("⚡ تشغيل صوت انفجار صوتي فرط صوتي واقعي");
        }
    }
    
    /// <summary>
    /// تشغيل صوت إصابة واقعي
    /// Play realistic hit sound
    /// </summary>
    public void PlayHitSound(Vector3 position, float intensity = 1f)
    {
        AudioClip clip = GetRandomClip("impact_hit");
        if (clip != null)
        {
            PlaySound3D(clip, position, intensity);
            OnSoundPlayed?.Invoke("impact_hit", clip);
            
            // تشغيل صوت عربي للإصابة
            StartCoroutine(PlayDelayedArabicSound("arabic_hit_confirmed", 0.3f));
        }
    }
    
    /// <summary>
    /// تشغيل صوت إعادة التحميل الواقعي
    /// Play realistic reload sound
    /// </summary>
    public void PlayReloadSound()
    {
        AudioClip clip = GetRandomClip("reload");
        if (clip != null)
        {
            sfxSource.PlayOneShot(clip, masterVolume * sfxVolume);
            OnSoundPlayed?.Invoke("reload", clip);
            
            // تشغيل صوت عربي لإعادة التحميل
            StartCoroutine(PlayDelayedArabicSound("arabic_reload", 0.5f));
        }
    }
    
    /// <summary>
    /// تشغيل الموسيقى الخلفية
    /// Play background music
    /// </summary>
    public void PlayBackgroundMusic(MusicType type = MusicType.Background)
    {
        string musicKey = "";
        switch (type)
        {
            case MusicType.Background:
                musicKey = "background_music";
                break;
            case MusicType.Combat:
                musicKey = "combat_music";
                break;
            case MusicType.Victory:
                musicKey = "victory_music";
                break;
        }
        
        AudioClip clip = GetRandomClip(musicKey);
        if (clip != null)
        {
            if (currentMusicCoroutine != null)
            {
                StopCoroutine(currentMusicCoroutine);
            }
            
            currentMusicCoroutine = StartCoroutine(FadeToNewMusic(clip));
            OnMusicChanged?.Invoke(musicKey, clip);
        }
    }
    
    /// <summary>
    /// تشغيل صوت ثلاثي الأبعاد
    /// Play 3D sound
    /// </summary>
    private void PlaySound3D(AudioClip clip, Vector3 position, float volume = 1f)
    {
        if (clip == null) return;
        
        AudioSource source = GetPooledAudioSource();
        if (source != null)
        {
            source.transform.position = position;
            source.clip = clip;
            source.volume = masterVolume * sfxVolume * volume;
            source.Play();
            
            StartCoroutine(ReturnSourceToPool(source, clip.length));
        }
    }
    
    /// <summary>
    /// الحصول على مصدر صوت من المجموعة
    /// Get pooled audio source
    /// </summary>
    private AudioSource GetPooledAudioSource()
    {
        if (audioSourcePool.Count > 0)
        {
            return audioSourcePool.Dequeue();
        }
        
        // إنشاء مصدر جديد إذا نفدت المجموعة
        GameObject sourceObj = new GameObject("TempRealAudioSource");
        sourceObj.transform.SetParent(transform);
        AudioSource newSource = sourceObj.AddComponent<AudioSource>();
        newSource.playOnAwake = false;
        newSource.spatialBlend = use3DAudio ? 1f : 0f;
        
        return newSource;
    }
    
    /// <summary>
    /// إرجاع مصدر الصوت للمجموعة
    /// Return source to pool
    /// </summary>
    private IEnumerator ReturnSourceToPool(AudioSource source, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (source != null)
        {
            source.Stop();
            source.clip = null;
            audioSourcePool.Enqueue(source);
        }
    }
    
    /// <summary>
    /// تشغيل صوت عربي متأخر
    /// Play delayed Arabic sound
    /// </summary>
    private IEnumerator PlayDelayedArabicSound(string soundKey, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        AudioClip clip = GetRandomClip(soundKey);
        if (clip != null)
        {
            voiceSource.PlayOneShot(clip, masterVolume * voiceVolume);
        }
    }
    
    /// <summary>
    /// تشغيل صوت حطام متأخر
    /// Play delayed debris sound
    /// </summary>
    private IEnumerator PlayDelayedDebrisSound(Vector3 position, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        AudioClip clip = GetRandomClip("debris_fall");
        if (clip != null)
        {
            PlaySound3D(clip, position, 0.7f);
        }
    }
    
    /// <summary>
    /// التلاشي إلى موسيقى جديدة
    /// Fade to new music
    /// </summary>
    private IEnumerator FadeToNewMusic(AudioClip newClip)
    {
        float originalVolume = musicSource.volume;
        
        // تلاشي الموسيقى الحالية
        while (musicSource.volume > 0)
        {
            musicSource.volume -= originalVolume * Time.deltaTime / 2f;
            yield return null;
        }
        
        // تشغيل الموسيقى الجديدة
        musicSource.clip = newClip;
        musicSource.Play();
        
        // رفع مستوى الصوت تدريجياً
        while (musicSource.volume < originalVolume)
        {
            musicSource.volume += originalVolume * Time.deltaTime / 2f;
            yield return null;
        }
        
        musicSource.volume = originalVolume;
    }
    
    /// <summary>
    /// الحصول على ملف صوتي عشوائي
    /// Get random audio clip
    /// </summary>
    private AudioClip GetRandomClip(string soundKey)
    {
        if (soundLibrary.ContainsKey(soundKey) && soundLibrary[soundKey].Length > 0)
        {
            AudioClip[] clips = soundLibrary[soundKey];
            return clips[Random.Range(0, clips.Length)];
        }
        
        return null;
    }
    
    /// <summary>
    /// تعيين مستوى الصوت الرئيسي
    /// Set master volume
    /// </summary>
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        UpdateAllVolumes();
    }

    /// <summary>
    /// تعيين مستوى صوت الطائرات
    /// Set aircraft volume
    /// </summary>
    public void SetAircraftVolume(float volume)
    {
        aircraftVolume = Mathf.Clamp01(volume);

        // تحديث أصوات الطائرات النشطة
        foreach (var source in activeAudioSources)
        {
            if (source != null && source.clip != null)
            {
                // فحص إذا كان الصوت من أصوات الطائرات
                string clipName = source.clip.name.ToLower();
                if (clipName.Contains("fighter") || clipName.Contains("helicopter") || clipName.Contains("aircraft"))
                {
                    source.volume = masterVolume * aircraftVolume;
                }
            }
        }

        Debug.Log($"🔊 تم تعديل مستوى صوت الطائرات إلى: {aircraftVolume * 100}%");
    }
    
    /// <summary>
    /// تحديث جميع مستويات الصوت
    /// Update all volumes
    /// </summary>
    private void UpdateAllVolumes()
    {
        if (musicSource != null)
            musicSource.volume = masterVolume * musicVolume;
        
        if (sfxSource != null)
            sfxSource.volume = masterVolume * sfxVolume;
        
        if (voiceSource != null)
            voiceSource.volume = masterVolume * voiceVolume;
        
        if (ambientSource != null)
            ambientSource.volume = masterVolume * ambientVolume;
    }
    
    /// <summary>
    /// إيقاف جميع الأصوات
    /// Stop all sounds
    /// </summary>
    public void StopAllSounds()
    {
        foreach (var source in activeAudioSources)
        {
            if (source != null)
            {
                source.Stop();
            }
        }
        
        activeAudioSources.Clear();
        
        if (musicSource != null) musicSource.Stop();
        if (sfxSource != null) sfxSource.Stop();
        if (voiceSource != null) voiceSource.Stop();
        if (ambientSource != null) ambientSource.Stop();
    }
    
    // تعدادات - Enums
    public enum ExplosionType
    {
        Small,
        Medium,
        Large
    }
    
    public enum AircraftType
    {
        Fighter,
        Helicopter,
        Generic
    }
    
    public enum MusicType
    {
        Background,
        Combat,
        Victory
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public bool IsInitialized => isInitialized;
    public float MasterVolume => masterVolume;
    public int LoadedSoundGroups => soundLibrary.Count;
}
