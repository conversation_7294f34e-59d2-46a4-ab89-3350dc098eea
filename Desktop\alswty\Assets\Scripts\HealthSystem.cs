using UnityEngine;
using UnityEngine.UI;
using System.Collections;

/// <summary>
/// نظام الصحة للأهداف والكائنات
/// Health System for targets and objects
/// </summary>
public class HealthSystem : MonoBehaviour
{
    [Header("إعدادات الصحة - Health Settings")]
    [SerializeField] private float maxHealth = 100f;
    [SerializeField] private float currentHealth;
    [SerializeField] private bool isInvulnerable = false;
    [SerializeField] private float invulnerabilityDuration = 0.5f;
    
    [Header("إعدادات التجديد - Regeneration Settings")]
    [SerializeField] private bool canRegenerate = false;
    [SerializeField] private float regenerationRate = 5f;
    [SerializeField] private float regenerationDelay = 3f;
    
    [Header("واجهة المستخدم - UI")]
    [SerializeField] private Slider healthBar;
    [SerializeField] private Image healthBarFill;
    [SerializeField] private Color healthyColor = Color.green;
    [SerializeField] private Color damagedColor = Color.yellow;
    [SerializeField] private Color criticalColor = Color.red;
    [SerializeField] private GameObject damageTextPrefab;
    [SerializeField] private Transform damageTextParent;
    
    [Header("المؤثرات - Effects")]
    [SerializeField] private ParticleSystem damageEffect;
    [SerializeField] private ParticleSystem healEffect;
    [SerializeField] private ParticleSystem deathEffect;
    [SerializeField] private AudioClip damageSound;
    [SerializeField] private AudioClip healSound;
    [SerializeField] private AudioClip deathSound;
    [SerializeField] private AudioSource audioSource;
    
    [Header("إعدادات الموت - Death Settings")]
    [SerializeField] private bool destroyOnDeath = true;
    [SerializeField] private float deathDelay = 2f;
    [SerializeField] private GameObject deathReplacement;
    
    // المتغيرات الداخلية - Internal Variables
    private bool isDead = false;
    private bool isCurrentlyInvulnerable = false;
    private float lastDamageTime;
    private Coroutine regenerationCoroutine;
    
    // الأحداث - Events
    public System.Action<float, float> OnHealthChanged; // current, max
    public System.Action<float> OnDamageTaken; // damage amount
    public System.Action<float> OnHealed; // heal amount
    public System.Action OnDeath;
    public System.Action OnRevived;
    
    void Start()
    {
        InitializeHealthSystem();
    }
    
    void Update()
    {
        UpdateHealthBar();
    }
    
    /// <summary>
    /// تهيئة نظام الصحة
    /// Initialize health system
    /// </summary>
    private void InitializeHealthSystem()
    {
        currentHealth = maxHealth;
        
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        UpdateHealthBar();
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    /// <summary>
    /// تلقي ضرر
    /// Take damage
    /// </summary>
    public void TakeDamage(float damage)
    {
        if (isDead || isCurrentlyInvulnerable || damage <= 0) return;
        
        // تطبيق الضرر
        currentHealth = Mathf.Max(0, currentHealth - damage);
        lastDamageTime = Time.time;
        
        // إيقاف التجديد
        if (regenerationCoroutine != null)
        {
            StopCoroutine(regenerationCoroutine);
            regenerationCoroutine = null;
        }
        
        // تشغيل المؤثرات
        PlayDamageEffects(damage);
        
        // عرض نص الضرر
        ShowDamageText(damage);
        
        // إشعار الأحداث
        OnDamageTaken?.Invoke(damage);
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
        
        // التحقق من الموت
        if (currentHealth <= 0)
        {
            Die();
        }
        else
        {
            // بدء فترة عدم التأثر
            if (invulnerabilityDuration > 0)
            {
                StartCoroutine(InvulnerabilityCoroutine());
            }
            
            // بدء التجديد إذا كان مفعلاً
            if (canRegenerate)
            {
                regenerationCoroutine = StartCoroutine(RegenerationCoroutine());
            }
        }
    }
    
    /// <summary>
    /// الشفاء
    /// Heal
    /// </summary>
    public void Heal(float healAmount)
    {
        if (isDead || healAmount <= 0) return;
        
        float actualHealAmount = Mathf.Min(healAmount, maxHealth - currentHealth);
        currentHealth += actualHealAmount;
        
        // تشغيل مؤثرات الشفاء
        PlayHealEffects(actualHealAmount);
        
        // عرض نص الشفاء
        ShowHealText(actualHealAmount);
        
        // إشعار الأحداث
        OnHealed?.Invoke(actualHealAmount);
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    /// <summary>
    /// الشفاء الكامل
    /// Full heal
    /// </summary>
    public void FullHeal()
    {
        Heal(maxHealth - currentHealth);
    }
    
    /// <summary>
    /// تعيين الصحة القصوى
    /// Set max health
    /// </summary>
    public void SetMaxHealth(float newMaxHealth)
    {
        float healthPercentage = currentHealth / maxHealth;
        maxHealth = newMaxHealth;
        currentHealth = maxHealth * healthPercentage;
        
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    /// <summary>
    /// الموت
    /// Die
    /// </summary>
    private void Die()
    {
        if (isDead) return;
        
        isDead = true;
        currentHealth = 0;
        
        // تشغيل مؤثرات الموت
        PlayDeathEffects();
        
        // إشعار الأحداث
        OnDeath?.Invoke();
        
        // تعطيل المكونات
        DisableComponents();
        
        // تدمير الكائن أو استبداله
        if (destroyOnDeath)
        {
            StartCoroutine(DeathCoroutine());
        }
    }
    
    /// <summary>
    /// الإحياء
    /// Revive
    /// </summary>
    public void Revive(float healthAmount = -1)
    {
        if (!isDead) return;
        
        isDead = false;
        currentHealth = healthAmount > 0 ? healthAmount : maxHealth;
        
        // إعادة تفعيل المكونات
        EnableComponents();
        
        // تشغيل مؤثرات الشفاء
        PlayHealEffects(currentHealth);
        
        // إشعار الأحداث
        OnRevived?.Invoke();
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    /// <summary>
    /// تشغيل مؤثرات الضرر
    /// Play damage effects
    /// </summary>
    private void PlayDamageEffects(float damage)
    {
        if (damageEffect != null)
        {
            damageEffect.Play();
        }
        
        if (damageSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(damageSound);
        }
        
        // تأثير اهتزاز الكاميرا (إذا كان متوفراً)
        CameraShake.Instance?.Shake(damage / maxHealth);
    }
    
    /// <summary>
    /// تشغيل مؤثرات الشفاء
    /// Play heal effects
    /// </summary>
    private void PlayHealEffects(float healAmount)
    {
        if (healEffect != null)
        {
            healEffect.Play();
        }
        
        if (healSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(healSound);
        }
    }
    
    /// <summary>
    /// تشغيل مؤثرات الموت
    /// Play death effects
    /// </summary>
    private void PlayDeathEffects()
    {
        if (deathEffect != null)
        {
            deathEffect.Play();
        }
        
        if (deathSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(deathSound);
        }
    }
    
    /// <summary>
    /// عرض نص الضرر
    /// Show damage text
    /// </summary>
    private void ShowDamageText(float damage)
    {
        if (damageTextPrefab == null) return;
        
        GameObject damageText = Instantiate(damageTextPrefab, 
                                          transform.position + Vector3.up * 2f, 
                                          Quaternion.identity, 
                                          damageTextParent);
        
        var textComponent = damageText.GetComponent<DamageText>();
        if (textComponent != null)
        {
            textComponent.ShowDamage(damage);
        }
    }
    
    /// <summary>
    /// عرض نص الشفاء
    /// Show heal text
    /// </summary>
    private void ShowHealText(float healAmount)
    {
        if (damageTextPrefab == null) return;
        
        GameObject healText = Instantiate(damageTextPrefab, 
                                        transform.position + Vector3.up * 2f, 
                                        Quaternion.identity, 
                                        damageTextParent);
        
        var textComponent = healText.GetComponent<DamageText>();
        if (textComponent != null)
        {
            textComponent.ShowHeal(healAmount);
        }
    }
    
    /// <summary>
    /// تحديث شريط الصحة
    /// Update health bar
    /// </summary>
    private void UpdateHealthBar()
    {
        if (healthBar == null) return;
        
        float healthPercentage = currentHealth / maxHealth;
        healthBar.value = healthPercentage;
        
        if (healthBarFill != null)
        {
            if (healthPercentage > 0.6f)
                healthBarFill.color = healthyColor;
            else if (healthPercentage > 0.3f)
                healthBarFill.color = damagedColor;
            else
                healthBarFill.color = criticalColor;
        }
    }
    
    /// <summary>
    /// عدم التأثر المؤقت
    /// Temporary invulnerability
    /// </summary>
    private IEnumerator InvulnerabilityCoroutine()
    {
        isCurrentlyInvulnerable = true;
        yield return new WaitForSeconds(invulnerabilityDuration);
        isCurrentlyInvulnerable = false;
    }
    
    /// <summary>
    /// تجديد الصحة
    /// Health regeneration
    /// </summary>
    private IEnumerator RegenerationCoroutine()
    {
        yield return new WaitForSeconds(regenerationDelay);
        
        while (currentHealth < maxHealth && !isDead)
        {
            float regenAmount = regenerationRate * Time.deltaTime;
            Heal(regenAmount);
            yield return null;
        }
    }
    
    /// <summary>
    /// عملية الموت
    /// Death coroutine
    /// </summary>
    private IEnumerator DeathCoroutine()
    {
        yield return new WaitForSeconds(deathDelay);
        
        if (deathReplacement != null)
        {
            Instantiate(deathReplacement, transform.position, transform.rotation);
        }
        
        Destroy(gameObject);
    }
    
    /// <summary>
    /// تعطيل المكونات
    /// Disable components
    /// </summary>
    private void DisableComponents()
    {
        var colliders = GetComponents<Collider>();
        foreach (var col in colliders)
        {
            col.enabled = false;
        }
        
        var rigidbody = GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            rigidbody.isKinematic = true;
        }
    }
    
    /// <summary>
    /// تفعيل المكونات
    /// Enable components
    /// </summary>
    private void EnableComponents()
    {
        var colliders = GetComponents<Collider>();
        foreach (var col in colliders)
        {
            col.enabled = true;
        }
        
        var rigidbody = GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            rigidbody.isKinematic = false;
        }
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public float CurrentHealth => currentHealth;
    public float MaxHealth => maxHealth;
    public float HealthPercentage => currentHealth / maxHealth;
    public bool IsDead => isDead;
    public bool IsInvulnerable => isInvulnerable || isCurrentlyInvulnerable;
    public bool CanRegenerate => canRegenerate;
}
