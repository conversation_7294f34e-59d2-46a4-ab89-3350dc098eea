<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الوعد الصادق 3 - مع الأصوات الواقعية</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><radialGradient id="sky" cx="50%" cy="0%" r="100%"><stop offset="0%" style="stop-color:%23001122"/><stop offset="50%" style="stop-color:%23003366"/><stop offset="100%" style="stop-color:%23000011"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23sky)"/><g opacity="0.3"><circle cx="200" cy="100" r="1" fill="white"/><circle cx="400" cy="150" r="1" fill="white"/><circle cx="600" cy="80" r="1" fill="white"/><circle cx="800" cy="120" r="1" fill="white"/><circle cx="1000" cy="90" r="1" fill="white"/><circle cx="1200" cy="140" r="1" fill="white"/><circle cx="1400" cy="110" r="1" fill="white"/><circle cx="1600" cy="160" r="1" fill="white"/></g></svg>') center/cover;
            font-family: 'Arial', sans-serif;
            color: white;
            overflow: hidden;
        }
        
        .game-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .title {
            font-size: 3em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 20px;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px #fff; }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px #fff, 0 0 30px #fff; }
        }
        
        .audio-status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #00ff88;
            font-size: 14px;
        }
        
        .audio-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #00ff88;
        }
        
        .volume-control {
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .volume-slider {
            width: 100px;
            height: 5px;
            background: #333;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 15px;
            height: 15px;
            background: #00ff88;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .start-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 20px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76,175,80,0.3);
        }
        
        .start-button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76,175,80,0.4);
        }
        
        .audio-info {
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #00ff88;
            max-width: 600px;
            text-align: center;
            margin: 20px;
        }
        
        .feature-list {
            text-align: right;
            margin: 15px 0;
            line-height: 1.8;
        }
        
        .feature-item {
            margin: 8px 0;
            padding: 5px 10px;
            background: rgba(0,255,136,0.1);
            border-radius: 5px;
            border-right: 3px solid #00ff88;
        }
        
        .upload-area {
            background: rgba(0,0,0,0.8);
            border: 3px dashed #00ff88;
            border-radius: 10px;
            padding: 30px;
            margin: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            background: rgba(0,255,136,0.1);
            border-color: #88ff88;
        }
        
        .upload-area.dragover {
            background: rgba(0,255,136,0.2);
            border-color: #ffff88;
        }
        
        .file-input {
            display: none;
        }
        
        .loaded-sounds {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #00ff88;
            margin: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .sound-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 10px;
            margin: 3px 0;
            background: rgba(0,255,136,0.1);
            border-radius: 5px;
        }
        
        .play-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .play-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1 class="title">🚀 الوعد الصادق 3</h1>
        <h2 style="color: #00ff88; margin-bottom: 30px;">النسخة مع الأصوات الواقعية</h2>
        
        <div class="audio-status" id="audioStatus">
            <div style="color: #00ff88; font-weight: bold;">🔊 حالة النظام الصوتي</div>
            <div id="audioStatusText">جاري التهيئة...</div>
            <div id="loadedSoundsCount">الأصوات المحملة: 0</div>
        </div>
        
        <div class="audio-controls">
            <div style="color: #00ff88; font-weight: bold; margin-bottom: 10px;">🎛️ التحكم في الصوت</div>
            <div class="volume-control">
                <span>الصوت الرئيسي:</span>
                <input type="range" class="volume-slider" id="masterVolume" min="0" max="100" value="80">
                <span id="masterVolumeValue">80%</span>
            </div>
            <div class="volume-control">
                <span>المؤثرات:</span>
                <input type="range" class="volume-slider" id="sfxVolume" min="0" max="100" value="90">
                <span id="sfxVolumeValue">90%</span>
            </div>
            <div class="volume-control">
                <span>الموسيقى:</span>
                <input type="range" class="volume-slider" id="musicVolume" min="0" max="100" value="70">
                <span id="musicVolumeValue">70%</span>
            </div>
            <div class="volume-control">
                <span>أصوات الطائرات:</span>
                <input type="range" class="volume-slider" id="aircraftVolume" min="0" max="100" value="20">
                <span id="aircraftVolumeValue">20%</span>
            </div>
        </div>
        
        <div class="audio-info">
            <h3 style="color: #ffff00;">🎵 نظام الأصوات الواقعية المتقدم</h3>
            <p>قم بتحميل الأصوات الواقعية لتجربة لعب مذهلة!</p>
            
            <div class="feature-list">
                <div class="feature-item">🚀 أصوات إطلاق صواريخ D610S واقعية</div>
                <div class="feature-item">💥 أصوات انفجارات عالية الجودة</div>
                <div class="feature-item">✈️ أصوات طائرات مقاتلة وهليكوبتر</div>
                <div class="feature-item">🎤 أصوات عربية للأوامر والتعليقات</div>
                <div class="feature-item">🎵 موسيقى خلفية وقتالية</div>
                <div class="feature-item">🔊 نظام صوتي ثلاثي الأبعاد</div>
            </div>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <h3 style="color: #00ff88;">📁 تحميل الأصوات الواقعية</h3>
            <p>اسحب وأفلت الملفات الصوتية هنا أو اضغط للاختيار</p>
            <p style="font-size: 14px; color: #aaa;">
                الصيغ المدعومة: WAV, MP3, OGG<br>
                الحد الأقصى: 10 MB لكل ملف
            </p>
            <input type="file" class="file-input" id="fileInput" multiple accept=".wav,.mp3,.ogg">
        </div>
        
        <div class="loaded-sounds" id="loadedSounds" style="display: none;">
            <h4 style="color: #00ff88; margin-bottom: 10px;">🎵 الأصوات المحملة</h4>
            <div id="soundsList"></div>
        </div>
        
        <button class="start-button" onclick="startGameWithRealAudio()" id="startButton" disabled>
            🚀 بدء اللعبة مع الأصوات الواقعية
        </button>
        
        <div style="margin-top: 20px; font-size: 14px; color: #aaa; text-align: center;">
            <p>💡 نصيحة: قم بتحميل الأصوات أولاً للحصول على أفضل تجربة</p>
            <p>🎮 يمكنك أيضاً بدء اللعبة بالأصوات الاصطناعية</p>
        </div>
    </div>

    <script>
        // متغيرات النظام الصوتي
        let audioContext;
        let loadedSounds = {};
        let soundsCount = 0;
        let audioInitialized = false;
        
        // إعدادات الصوت
        let masterVolume = 0.8;
        let sfxVolume = 0.9;
        let musicVolume = 0.7;
        let aircraftVolume = 0.2; // مستوى منخفض للطائرات
        
        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', function() {
            initializeAudioSystem();
            setupEventListeners();
            updateAudioStatus();
        });
        
        // تهيئة النظام الصوتي
        function initializeAudioSystem() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                audioInitialized = true;
                updateAudioStatus('✅ النظام الصوتي جاهز');
                document.getElementById('startButton').disabled = false;
                console.log('🔊 تم تهيئة النظام الصوتي بنجاح');
            } catch (error) {
                updateAudioStatus('❌ خطأ في تهيئة النظام الصوتي');
                console.error('خطأ في النظام الصوتي:', error);
            }
        }
        
        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // أحداث السحب والإفلات
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
            
            // النقر لاختيار الملفات
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });
            
            // أحداث التحكم في الصوت
            document.getElementById('masterVolume').addEventListener('input', function(e) {
                masterVolume = e.target.value / 100;
                document.getElementById('masterVolumeValue').textContent = e.target.value + '%';
            });
            
            document.getElementById('sfxVolume').addEventListener('input', function(e) {
                sfxVolume = e.target.value / 100;
                document.getElementById('sfxVolumeValue').textContent = e.target.value + '%';
            });
            
            document.getElementById('musicVolume').addEventListener('input', function(e) {
                musicVolume = e.target.value / 100;
                document.getElementById('musicVolumeValue').textContent = e.target.value + '%';
            });

            document.getElementById('aircraftVolume').addEventListener('input', function(e) {
                aircraftVolume = e.target.value / 100;
                document.getElementById('aircraftVolumeValue').textContent = e.target.value + '%';
                console.log(`🔊 تم تعديل مستوى صوت الطائرات إلى: ${e.target.value}%`);
            });
        }
        
        // معالجة الملفات المحملة
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (isValidAudioFile(file)) {
                    loadAudioFile(file);
                } else {
                    alert(`ملف غير مدعوم: ${file.name}\nالصيغ المدعومة: WAV, MP3, OGG`);
                }
            });
        }
        
        // فحص صحة الملف الصوتي
        function isValidAudioFile(file) {
            const validTypes = ['audio/wav', 'audio/mpeg', 'audio/ogg', 'audio/mp3'];
            const validExtensions = ['.wav', '.mp3', '.ogg'];
            
            return validTypes.includes(file.type) || 
                   validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        }
        
        // تحميل ملف صوتي
        function loadAudioFile(file) {
            if (file.size > 10 * 1024 * 1024) { // 10 MB
                alert(`الملف كبير جداً: ${file.name}\nالحد الأقصى: 10 MB`);
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                audioContext.decodeAudioData(e.target.result)
                    .then(audioBuffer => {
                        const soundName = file.name.replace(/\.[^/.]+$/, "");
                        loadedSounds[soundName] = audioBuffer;
                        soundsCount++;
                        
                        addSoundToList(soundName, file.name);
                        updateLoadedSoundsCount();
                        
                        console.log(`✅ تم تحميل الصوت: ${file.name}`);
                    })
                    .catch(error => {
                        console.error(`❌ خطأ في تحميل الصوت ${file.name}:`, error);
                        alert(`خطأ في تحميل الصوت: ${file.name}`);
                    });
            };
            
            reader.readAsArrayBuffer(file);
        }
        
        // إضافة صوت إلى القائمة
        function addSoundToList(soundName, fileName) {
            const soundsList = document.getElementById('soundsList');
            const loadedSoundsDiv = document.getElementById('loadedSounds');
            
            const soundItem = document.createElement('div');
            soundItem.className = 'sound-item';
            soundItem.innerHTML = `
                <span>${fileName}</span>
                <button class="play-button" onclick="testPlaySound('${soundName}')">▶️ تشغيل</button>
            `;
            
            soundsList.appendChild(soundItem);
            loadedSoundsDiv.style.display = 'block';
        }
        
        // تشغيل صوت للاختبار
        function testPlaySound(soundName) {
            if (loadedSounds[soundName] && audioContext) {
                const source = audioContext.createBufferSource();
                const gainNode = audioContext.createGain();
                
                source.buffer = loadedSounds[soundName];
                source.connect(gainNode);
                gainNode.connect(audioContext.destination);
                gainNode.gain.value = masterVolume * sfxVolume;
                
                source.start();
                console.log(`🔊 تشغيل اختباري للصوت: ${soundName}`);
            }
        }
        
        // تحديث حالة النظام الصوتي
        function updateAudioStatus(message = null) {
            const statusText = document.getElementById('audioStatusText');
            
            if (message) {
                statusText.textContent = message;
            } else if (audioInitialized) {
                statusText.textContent = '✅ النظام الصوتي نشط';
                statusText.style.color = '#00ff88';
            } else {
                statusText.textContent = '❌ النظام الصوتي غير متاح';
                statusText.style.color = '#ff4444';
            }
        }
        
        // تحديث عدد الأصوات المحملة
        function updateLoadedSoundsCount() {
            document.getElementById('loadedSoundsCount').textContent = `الأصوات المحملة: ${soundsCount}`;
        }
        
        // بدء اللعبة مع الأصوات الواقعية
        function startGameWithRealAudio() {
            if (!audioInitialized) {
                alert('❌ النظام الصوتي غير متاح!\nتحقق من إعدادات المتصفح.');
                return;
            }
            
            // تمرير الأصوات المحملة إلى اللعبة
            const gameData = {
                audioContext: audioContext,
                loadedSounds: loadedSounds,
                audioSettings: {
                    masterVolume: masterVolume,
                    sfxVolume: sfxVolume,
                    musicVolume: musicVolume,
                    aircraftVolume: aircraftVolume
                }
            };
            
            // حفظ البيانات في localStorage للوصول إليها من اللعبة
            localStorage.setItem('gameAudioData', JSON.stringify({
                soundsCount: soundsCount,
                audioSettings: gameData.audioSettings
            }));
            
            console.log('🚀 بدء اللعبة مع الأصوات الواقعية');
            console.log(`📊 تم تحميل ${soundsCount} صوت واقعي`);
            
            // فتح اللعبة الرئيسية
            window.location.href = 'TestGame.html?realAudio=true';
        }
        
        // رسائل ترحيبية
        console.log('🎵 مرحباً بك في نظام الأصوات الواقعية');
        console.log('📁 قم بتحميل الأصوات الواقعية للحصول على أفضل تجربة');
        console.log('🔊 النظام يدعم: WAV, MP3, OGG');
        console.log('⚡ الحد الأقصى: 10 MB لكل ملف');
    </script>
</body>
</html>
