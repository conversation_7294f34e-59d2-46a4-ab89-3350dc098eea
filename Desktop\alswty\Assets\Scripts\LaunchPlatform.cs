using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// منصة إطلاق الصواريخ فرط الصوتية
/// Hypersonic Missile Launch Platform
/// </summary>
public class LaunchPlatform : MonoBehaviour
{
    [Header("إعدادات المنصة - Platform Settings")]
    [SerializeField] private Transform launchPoint;
    [SerializeField] private Transform rotationBase;
    [SerializeField] private float rotationSpeed = 45f;
    [SerializeField] private float elevationSpeed = 30f;
    [SerializeField] private float minElevation = -10f;
    [SerializeField] private float maxElevation = 85f;
    
    [Header("إعدادات الصواريخ - Missile Settings")]
    [SerializeField] private GameObject missilePrefab;
    [SerializeField] private int maxMissileCount = 10;
    [SerializeField] private float reloadTime = 3f;
    [SerializeField] private float launchCooldown = 1f;
    
    [Header("إعدادات الاستهداف - Targeting Settings")]
    [SerializeField] private Camera targetingCamera;
    [SerializeField] private LayerMask targetLayers = -1;
    [SerializeField] private float maxTargetingRange = 1000f;
    [SerializeField] private bool autoTargeting = false;
    [SerializeField] private float autoTargetScanRadius = 500f;
    
    [Header("المؤثرات - Effects")]
    [SerializeField] private ParticleSystem launchFlash;
    [SerializeField] private ParticleSystem smokeEffect;
    [SerializeField] private AudioClip launchSound;
    [SerializeField] private AudioClip reloadSound;
    [SerializeField] private AudioSource audioSource;
    
    [Header("واجهة المستخدم - UI")]
    [SerializeField] private GameObject crosshair;
    [SerializeField] private LineRenderer trajectoryLine;
    [SerializeField] private int trajectoryPoints = 50;
    
    // المتغيرات الداخلية - Internal Variables
    private int currentMissileCount;
    private bool isReloading = false;
    private bool canLaunch = true;
    private float lastLaunchTime;
    private Transform currentTarget;
    private Vector3 targetPosition;
    private float currentElevation = 0f;
    private float currentRotation = 0f;
    
    // قائمة الصواريخ النشطة - Active Missiles List
    private List<MissileController> activeMissiles = new List<MissileController>();
    
    // الأحداث - Events
    public System.Action<int> OnMissileCountChanged;
    public System.Action<bool> OnReloadStateChanged;
    public System.Action<Transform> OnTargetChanged;
    
    void Start()
    {
        InitializePlatform();
    }
    
    void Update()
    {
        HandleInput();
        UpdateTargeting();
        UpdateTrajectoryPreview();
        CleanupMissileList();
    }
    
    /// <summary>
    /// تهيئة المنصة
    /// Initialize the platform
    /// </summary>
    private void InitializePlatform()
    {
        currentMissileCount = maxMissileCount;
        
        if (launchPoint == null)
        {
            launchPoint = transform;
        }
        
        if (rotationBase == null)
        {
            rotationBase = transform;
        }
        
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        if (trajectoryLine != null)
        {
            trajectoryLine.positionCount = trajectoryPoints;
            trajectoryLine.enabled = false;
        }
        
        // إشعار واجهة المستخدم
        OnMissileCountChanged?.Invoke(currentMissileCount);
    }
    
    /// <summary>
    /// التحكم في الإدخال
    /// Handle input
    /// </summary>
    private void HandleInput()
    {
        // التحكم بالماوس للتصويب
        HandleMouseAiming();
        
        // التحكم بلوحة المفاتيح
        HandleKeyboardInput();
        
        // إطلاق الصاروخ
        if (Input.GetKeyDown(KeyCode.Space) || Input.GetMouseButtonDown(0))
        {
            LaunchMissile();
        }
        
        // إعادة التحميل
        if (Input.GetKeyDown(KeyCode.R))
        {
            StartReload();
        }
        
        // تبديل وضع الاستهداف التلقائي
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            autoTargeting = !autoTargeting;
        }
    }
    
    /// <summary>
    /// التحكم بالتصويب بالماوس
    /// Handle mouse aiming
    /// </summary>
    private void HandleMouseAiming()
    {
        if (autoTargeting) return;
        
        // الحصول على موقع الماوس في الشاشة
        Vector3 mousePosition = Input.mousePosition;
        
        if (targetingCamera != null)
        {
            Ray ray = targetingCamera.ScreenPointToRay(mousePosition);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, maxTargetingRange, targetLayers))
            {
                targetPosition = hit.point;
                currentTarget = hit.transform;
                
                // تحديث اتجاه المنصة
                UpdatePlatformRotation(targetPosition);
            }
        }
    }
    
    /// <summary>
    /// التحكم بلوحة المفاتيح
    /// Handle keyboard input
    /// </summary>
    private void HandleKeyboardInput()
    {
        if (autoTargeting) return;
        
        // التحكم اليدوي بالدوران
        float horizontalInput = Input.GetAxis("Horizontal");
        float verticalInput = Input.GetAxis("Vertical");
        
        if (Mathf.Abs(horizontalInput) > 0.1f)
        {
            currentRotation += horizontalInput * rotationSpeed * Time.deltaTime;
            currentRotation = Mathf.Repeat(currentRotation, 360f);
        }
        
        if (Mathf.Abs(verticalInput) > 0.1f)
        {
            currentElevation += verticalInput * elevationSpeed * Time.deltaTime;
            currentElevation = Mathf.Clamp(currentElevation, minElevation, maxElevation);
        }
        
        // تطبيق الدوران
        if (rotationBase != null)
        {
            rotationBase.rotation = Quaternion.Euler(currentElevation, currentRotation, 0f);
        }
    }
    
    /// <summary>
    /// تحديث نظام الاستهداف
    /// Update targeting system
    /// </summary>
    private void UpdateTargeting()
    {
        if (!autoTargeting) return;
        
        // البحث عن أقرب هدف
        Collider[] potentialTargets = Physics.OverlapSphere(transform.position, autoTargetScanRadius, targetLayers);
        
        float closestDistance = float.MaxValue;
        Transform closestTarget = null;
        
        foreach (Collider col in potentialTargets)
        {
            if (col.transform == transform) continue;
            
            // التحقق من أن الهدف في خط البصر
            Vector3 directionToTarget = col.transform.position - launchPoint.position;
            if (Physics.Raycast(launchPoint.position, directionToTarget.normalized, 
                              directionToTarget.magnitude, ~targetLayers))
            {
                continue; // هناك عائق
            }
            
            float distance = Vector3.Distance(transform.position, col.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestTarget = col.transform;
            }
        }
        
        if (closestTarget != currentTarget)
        {
            currentTarget = closestTarget;
            OnTargetChanged?.Invoke(currentTarget);
            
            if (currentTarget != null)
            {
                targetPosition = currentTarget.position;
                UpdatePlatformRotation(targetPosition);
            }
        }
        
        // تحديث موقع الهدف إذا كان متحركاً
        if (currentTarget != null)
        {
            targetPosition = currentTarget.position;
            UpdatePlatformRotation(targetPosition);
        }
    }
    
    /// <summary>
    /// تحديث دوران المنصة
    /// Update platform rotation
    /// </summary>
    private void UpdatePlatformRotation(Vector3 target)
    {
        Vector3 direction = (target - launchPoint.position).normalized;
        
        // حساب الزوايا المطلوبة
        float targetRotation = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg;
        float targetElevation = Mathf.Asin(direction.y) * Mathf.Rad2Deg;
        
        // تطبيق القيود
        targetElevation = Mathf.Clamp(targetElevation, minElevation, maxElevation);
        
        // التحرك التدريجي نحو الهدف
        currentRotation = Mathf.LerpAngle(currentRotation, targetRotation, rotationSpeed * Time.deltaTime / 45f);
        currentElevation = Mathf.Lerp(currentElevation, targetElevation, elevationSpeed * Time.deltaTime / 30f);
        
        // تطبيق الدوران
        if (rotationBase != null)
        {
            rotationBase.rotation = Quaternion.Euler(currentElevation, currentRotation, 0f);
        }
    }
    
    /// <summary>
    /// تحديث معاينة المسار
    /// Update trajectory preview
    /// </summary>
    private void UpdateTrajectoryPreview()
    {
        if (trajectoryLine == null || currentTarget == null) return;
        
        trajectoryLine.enabled = true;
        
        // حساب نقاط المسار
        Vector3 startPosition = launchPoint.position;
        Vector3 startVelocity = launchPoint.forward * 100f; // سرعة افتراضية
        
        for (int i = 0; i < trajectoryPoints; i++)
        {
            float time = i * 0.1f;
            Vector3 point = CalculateTrajectoryPoint(startPosition, startVelocity, time);
            trajectoryLine.SetPosition(i, point);
            
            // التوقف إذا اصطدم المسار بشيء
            if (Physics.Raycast(point, Vector3.down, 1f))
            {
                trajectoryLine.positionCount = i + 1;
                break;
            }
        }
    }
    
    /// <summary>
    /// حساب نقطة في المسار
    /// Calculate trajectory point
    /// </summary>
    private Vector3 CalculateTrajectoryPoint(Vector3 startPos, Vector3 startVel, float time)
    {
        Vector3 gravity = Vector3.down * 9.81f;
        return startPos + startVel * time + 0.5f * gravity * time * time;
    }
    
    /// <summary>
    /// إطلاق صاروخ
    /// Launch a missile
    /// </summary>
    public void LaunchMissile()
    {
        if (!CanLaunch()) return;
        
        // إنشاء الصاروخ
        GameObject missileObj = Instantiate(missilePrefab, launchPoint.position, launchPoint.rotation);
        MissileController missile = missileObj.GetComponent<MissileController>();
        
        if (missile != null)
        {
            // تعيين الهدف
            missile.LaunchMissile(currentTarget);
            
            // إضافة الصاروخ إلى القائمة
            activeMissiles.Add(missile);
            
            // ربط الأحداث
            missile.OnMissileDestroyed += OnMissileDestroyed;
            missile.OnTargetHit += OnMissileHitTarget;
        }
        
        // تقليل عدد الصواريخ
        currentMissileCount--;
        OnMissileCountChanged?.Invoke(currentMissileCount);
        
        // تشغيل المؤثرات
        PlayLaunchEffects();
        
        // تسجيل وقت الإطلاق
        lastLaunchTime = Time.time;
        
        // بدء إعادة التحميل التلقائي إذا نفدت الصواريخ
        if (currentMissileCount <= 0)
        {
            StartReload();
        }
    }
    
    /// <summary>
    /// التحقق من إمكانية الإطلاق
    /// Check if can launch
    /// </summary>
    private bool CanLaunch()
    {
        return canLaunch && 
               currentMissileCount > 0 && 
               !isReloading && 
               Time.time - lastLaunchTime >= launchCooldown;
    }
    
    /// <summary>
    /// تشغيل مؤثرات الإطلاق
    /// Play launch effects
    /// </summary>
    private void PlayLaunchEffects()
    {
        if (launchFlash != null)
        {
            launchFlash.Play();
        }
        
        if (smokeEffect != null)
        {
            smokeEffect.Play();
        }
        
        if (launchSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(launchSound);
        }
    }
    
    /// <summary>
    /// بدء إعادة التحميل
    /// Start reload
    /// </summary>
    public void StartReload()
    {
        if (isReloading || currentMissileCount >= maxMissileCount) return;
        
        StartCoroutine(ReloadCoroutine());
    }
    
    /// <summary>
    /// عملية إعادة التحميل
    /// Reload coroutine
    /// </summary>
    private IEnumerator ReloadCoroutine()
    {
        isReloading = true;
        OnReloadStateChanged?.Invoke(true);
        
        // تشغيل صوت إعادة التحميل
        if (reloadSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(reloadSound);
        }
        
        yield return new WaitForSeconds(reloadTime);
        
        // إعادة ملء الصواريخ
        currentMissileCount = maxMissileCount;
        isReloading = false;
        
        OnMissileCountChanged?.Invoke(currentMissileCount);
        OnReloadStateChanged?.Invoke(false);
    }
    
    /// <summary>
    /// عند تدمير صاروخ
    /// When missile is destroyed
    /// </summary>
    private void OnMissileDestroyed(MissileController missile)
    {
        if (activeMissiles.Contains(missile))
        {
            activeMissiles.Remove(missile);
        }
    }
    
    /// <summary>
    /// عند إصابة هدف
    /// When missile hits target
    /// </summary>
    private void OnMissileHitTarget(MissileController missile, Collider target)
    {
        // يمكن إضافة منطق إضافي هنا مثل النقاط أو الإحصائيات
        Debug.Log($"تم إصابة الهدف: {target.name}");
    }
    
    /// <summary>
    /// تنظيف قائمة الصواريخ
    /// Cleanup missile list
    /// </summary>
    private void CleanupMissileList()
    {
        activeMissiles.RemoveAll(missile => missile == null);
    }
    
    /// <summary>
    /// رسم المساعدات البصرية
    /// Draw visual aids
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        // رسم نطاق الاستهداف التلقائي
        if (autoTargeting)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(transform.position, autoTargetScanRadius);
        }
        
        // رسم نطاق الاستهداف الأقصى
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, maxTargetingRange);
        
        // رسم خط إلى الهدف الحالي
        if (currentTarget != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawLine(launchPoint.position, currentTarget.position);
        }
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public int CurrentMissileCount => currentMissileCount;
    public int MaxMissileCount => maxMissileCount;
    public bool IsReloading => isReloading;
    public bool AutoTargeting => autoTargeting;
    public Transform CurrentTarget => currentTarget;
    public List<MissileController> ActiveMissiles => new List<MissileController>(activeMissiles);
}
