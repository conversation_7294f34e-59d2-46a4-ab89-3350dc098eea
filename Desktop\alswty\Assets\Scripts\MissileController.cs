using UnityEngine;
using System.Collections;

/// <summary>
/// تحكم في حركة وسلوك الصاروخ فرط الصوتي
/// Hypersonic Missile Controller
/// </summary>
public class MissileController : MonoBehaviour
{
    [Header("إعدادات الصاروخ - Missile Settings")]
    [SerializeField] private float speed = 100f;
    [SerializeField] private float maxSpeed = 300f;
    [SerializeField] private float acceleration = 50f;
    [SerializeField] private float turnSpeed = 90f;
    [SerializeField] private float fuel = 100f;
    [SerializeField] private float fuelConsumption = 10f;
    
    [Header("إعدادات الاستهداف - Targeting Settings")]
    [SerializeField] private Transform target;
    [SerializeField] private float detectionRange = 200f;
    [SerializeField] private LayerMask targetLayers = -1;
    [SerializeField] private bool heatSeeking = true;
    [SerializeField] private bool laserGuided = false;
    
    [Header("إعدادات الفيزياء - Physics Settings")]
    [SerializeField] private float gravity = 9.81f;
    [SerializeField] private float airResistance = 0.1f;
    [SerializeField] private AnimationCurve dragCurve;
    
    [Header("إعدادات الانفجار - Explosion Settings")]
    [SerializeField] private float explosionRadius = 50f;
    [SerializeField] private float explosionDamage = 100f;
    [SerializeField] private GameObject explosionEffect;
    [SerializeField] private AudioClip explosionSound;
    
    [Header("المؤثرات - Effects")]
    [SerializeField] private ParticleSystem thrusterEffect;
    [SerializeField] private ParticleSystem smokeTrail;
    [SerializeField] private AudioSource engineSound;
    [SerializeField] private Light thrusterLight;
    
    // المتغيرات الداخلية - Internal Variables
    private Rigidbody rb;
    private bool isLaunched = false;
    private bool isActive = true;
    private Vector3 velocity;
    private float currentSpeed;
    private float currentFuel;
    private Transform originalTarget;
    
    // الأحداث - Events
    public System.Action<MissileController> OnMissileDestroyed;
    public System.Action<MissileController, Collider> OnTargetHit;
    
    void Start()
    {
        InitializeMissile();
    }
    
    void Update()
    {
        if (!isActive || !isLaunched) return;
        
        UpdateMissileMovement();
        UpdateTargeting();
        UpdateEffects();
        UpdateFuel();
    }
    
    void FixedUpdate()
    {
        if (!isActive || !isLaunched) return;
        
        ApplyPhysics();
    }
    
    /// <summary>
    /// تهيئة الصاروخ
    /// Initialize the missile
    /// </summary>
    private void InitializeMissile()
    {
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = gameObject.AddComponent<Rigidbody>();
        }
        
        rb.useGravity = false; // سنتحكم في الجاذبية يدوياً
        currentFuel = fuel;
        currentSpeed = 0f;
        
        // تهيئة المؤثرات
        if (thrusterEffect != null) thrusterEffect.Stop();
        if (smokeTrail != null) smokeTrail.Stop();
        if (engineSound != null) engineSound.Stop();
        if (thrusterLight != null) thrusterLight.enabled = false;
    }
    
    /// <summary>
    /// إطلاق الصاروخ
    /// Launch the missile
    /// </summary>
    public void LaunchMissile(Transform targetTransform = null)
    {
        if (isLaunched) return;
        
        isLaunched = true;
        isActive = true;
        
        if (targetTransform != null)
        {
            target = targetTransform;
            originalTarget = targetTransform;
        }
        
        // تشغيل المؤثرات
        if (thrusterEffect != null) thrusterEffect.Play();
        if (smokeTrail != null) smokeTrail.Play();
        if (engineSound != null) engineSound.Play();
        if (thrusterLight != null) thrusterLight.enabled = true;
        
        // إعطاء دفعة أولية
        velocity = transform.forward * speed;
    }
    
    /// <summary>
    /// تحديث حركة الصاروخ
    /// Update missile movement
    /// </summary>
    private void UpdateMissileMovement()
    {
        if (currentFuel <= 0) return;
        
        // زيادة السرعة تدريجياً
        currentSpeed = Mathf.Min(currentSpeed + acceleration * Time.deltaTime, maxSpeed);
        
        // التوجه نحو الهدف
        if (target != null)
        {
            Vector3 directionToTarget = (target.position - transform.position).normalized;
            Vector3 newDirection = Vector3.Slerp(transform.forward, directionToTarget, 
                                               turnSpeed * Time.deltaTime / 180f);
            transform.rotation = Quaternion.LookRotation(newDirection);
        }
        
        // تحديث السرعة
        velocity = transform.forward * currentSpeed;
    }
    
    /// <summary>
    /// تحديث نظام الاستهداف
    /// Update targeting system
    /// </summary>
    private void UpdateTargeting()
    {
        if (target == null || !heatSeeking) return;
        
        // البحث عن أهداف جديدة في حالة فقدان الهدف الأصلي
        if (Vector3.Distance(transform.position, target.position) > detectionRange)
        {
            FindNewTarget();
        }
    }
    
    /// <summary>
    /// البحث عن هدف جديد
    /// Find a new target
    /// </summary>
    private void FindNewTarget()
    {
        Collider[] potentialTargets = Physics.OverlapSphere(transform.position, detectionRange, targetLayers);
        
        float closestDistance = float.MaxValue;
        Transform closestTarget = null;
        
        foreach (Collider col in potentialTargets)
        {
            if (col.transform == transform) continue;
            
            float distance = Vector3.Distance(transform.position, col.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestTarget = col.transform;
            }
        }
        
        if (closestTarget != null)
        {
            target = closestTarget;
        }
    }
    
    /// <summary>
    /// تطبيق الفيزياء
    /// Apply physics
    /// </summary>
    private void ApplyPhysics()
    {
        // تطبيق الجاذبية
        velocity.y -= gravity * Time.fixedDeltaTime;
        
        // تطبيق مقاومة الهواء
        float dragForce = airResistance * velocity.sqrMagnitude;
        if (dragCurve != null)
        {
            dragForce *= dragCurve.Evaluate(currentSpeed / maxSpeed);
        }
        
        velocity *= (1f - dragForce * Time.fixedDeltaTime);
        
        // تطبيق الحركة
        rb.velocity = velocity;
    }
    
    /// <summary>
    /// تحديث الوقود
    /// Update fuel
    /// </summary>
    private void UpdateFuel()
    {
        if (currentFuel > 0)
        {
            currentFuel -= fuelConsumption * Time.deltaTime;
            
            if (currentFuel <= 0)
            {
                // نفاد الوقود
                OnFuelDepleted();
            }
        }
    }
    
    /// <summary>
    /// تحديث المؤثرات
    /// Update effects
    /// </summary>
    private void UpdateEffects()
    {
        // تحديث شدة المؤثرات حسب السرعة والوقود
        float intensityFactor = currentSpeed / maxSpeed;
        float fuelFactor = currentFuel / fuel;
        
        if (thrusterEffect != null)
        {
            var emission = thrusterEffect.emission;
            emission.rateOverTime = 100f * intensityFactor * fuelFactor;
        }
        
        if (thrusterLight != null)
        {
            thrusterLight.intensity = 2f * intensityFactor * fuelFactor;
        }
        
        if (engineSound != null)
        {
            engineSound.pitch = 0.5f + (intensityFactor * 1.5f);
            engineSound.volume = 0.3f + (intensityFactor * 0.7f);
        }
    }
    
    /// <summary>
    /// عند نفاد الوقود
    /// When fuel is depleted
    /// </summary>
    private void OnFuelDepleted()
    {
        // إيقاف المؤثرات
        if (thrusterEffect != null) thrusterEffect.Stop();
        if (engineSound != null) engineSound.Stop();
        if (thrusterLight != null) thrusterLight.enabled = false;
        
        // تفعيل الجاذبية الكاملة
        rb.useGravity = true;
    }
    
    /// <summary>
    /// عند الاصطدام
    /// On collision
    /// </summary>
    private void OnTriggerEnter(Collider other)
    {
        if (!isActive) return;
        
        // تجاهل الاصطدام مع منصة الإطلاق
        if (other.CompareTag("LaunchPlatform")) return;
        
        // انفجار الصاروخ
        ExplodeMissile(other);
    }
    
    /// <summary>
    /// انفجار الصاروخ
    /// Explode the missile
    /// </summary>
    private void ExplodeMissile(Collider hitTarget = null)
    {
        isActive = false;
        
        // إنشاء تأثير الانفجار
        if (explosionEffect != null)
        {
            GameObject explosion = Instantiate(explosionEffect, transform.position, Quaternion.identity);
            Destroy(explosion, 5f);
        }
        
        // تشغيل صوت الانفجار
        if (explosionSound != null)
        {
            AudioSource.PlayClipAtPoint(explosionSound, transform.position);
        }
        
        // إلحاق الضرر بالأهداف في نطاق الانفجار
        DealExplosionDamage();
        
        // إشعار النظام بتدمير الصاروخ
        OnMissileDestroyed?.Invoke(this);
        if (hitTarget != null)
        {
            OnTargetHit?.Invoke(this, hitTarget);
        }
        
        // تدمير الصاروخ
        Destroy(gameObject, 0.1f);
    }
    
    /// <summary>
    /// إلحاق ضرر الانفجار
    /// Deal explosion damage
    /// </summary>
    private void DealExplosionDamage()
    {
        Collider[] affectedObjects = Physics.OverlapSphere(transform.position, explosionRadius);
        
        foreach (Collider col in affectedObjects)
        {
            // البحث عن مكون الصحة
            var healthComponent = col.GetComponent<HealthSystem>();
            if (healthComponent != null)
            {
                float distance = Vector3.Distance(transform.position, col.transform.position);
                float damageMultiplier = 1f - (distance / explosionRadius);
                float finalDamage = explosionDamage * damageMultiplier;
                
                healthComponent.TakeDamage(finalDamage);
            }
            
            // تطبيق قوة الانفجار
            var rb = col.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.AddExplosionForce(explosionDamage * 10f, transform.position, explosionRadius);
            }
        }
    }
    
    /// <summary>
    /// رسم المساعدات البصرية في المحرر
    /// Draw visual aids in editor
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        // رسم نطاق الكشف
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // رسم نطاق الانفجار
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, explosionRadius);
        
        // رسم خط إلى الهدف
        if (target != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, target.position);
        }
    }
    
    // خصائص للوصول من الخارج - Public Properties
    public bool IsLaunched => isLaunched;
    public bool IsActive => isActive;
    public float CurrentSpeed => currentSpeed;
    public float CurrentFuel => currentFuel;
    public Transform CurrentTarget => target;
}
